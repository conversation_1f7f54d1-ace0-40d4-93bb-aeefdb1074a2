<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="theme-color" content="#2EBAC6" />
    <meta name="author" content="P.R">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <g:render template="/wonderpublish/loginChecker"></g:render>
    <% if("arihant".equals(session["entryController"])){%>
    <link rel="icon"  href="${assetPath(src: 'arihant/favicon.png')}" type="image/x-icon">
    <%}%>
    <% if("radianbooks".equals(session["entryController"])){%>
    <link rel="shortcut icon" href="${assetPath(src: 'radianbooks/radianbooks-logo.png')}" type="image/x-icon"/>
    <%}%>
    <% if("oswaal".equals(session["entryController"])){%>
    <link rel="icon" href="${assetPath(src: 'oswaal/favicon.ico')}" type="image/x-icon"/>
    <%}%>
    <% if("books".equals(session["entryController"])){%>
    <link rel="icon"  href="${assetPath(src: 'landingpageImages/favicon.ico')}" type="image/x-icon">
    <%}%>
    <%if("true".equals(session["commonWhiteLabel"])){%>
    <link rel="icon"  href="/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["favicon"]}" type="image/x-icon">
    <%}%>
    <% if("libwonder".equals(session["entryController"])){%>
    <link rel="icon"  href="${assetPath(src: 'landingpageImages/favicon.ico')}" type="image/x-icon">
    <%} else {%>
    <link rel="icon" href="${assetPath(src: "favicons/${session['entryController']}.png")}" type="image/x-icon">
    <%}%>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css" integrity="sha512-KfkfwYDsLkIlwQp6LFnl8zNdLGxu9YAA1QvwINks4PhcElQSvqcyVLLD9aMhXd13uQjoXtEKNosOWaZqXgel0g==" crossorigin="anonymous" referrerpolicy="no-referrer" />    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;400;500&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Righteous&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/assets/landingpage/bootstrap.min.css"/>
    <link rel="stylesheet" href="/assets/prepJoy/animate.css"/>
    <link rel="stylesheet" href="/assets/prepJoy/jquery-ui.css">
    <link rel="stylesheet" id="webmcqStyle" href="/assets/prepJoy/prepjoyWebsites/newMcqStyle.css">
    <link rel="stylesheet" id="webmcqStyle" href="/assets/prepJoy/play.css">
    <script src="/assets/prepjoy/bootstrap.min.js" ></script>
    <script src="/assets/prepjoy/swiper.js"></script>

    <%if("39".equals(""+session["siteId"])){%>
    <style>
        body.arihant .nav-tabs{
            top:0px !important
        }
        @media (max-width:575px) {
            body.arihant.radian_books .nav-tabs {
                top: 0px !important;
            }
            body.arihant .nav-tabs {
                top:0px !important
            }
        }

    </style>
    <%}%>

    <style>
        #closeButton{
            display: none;
        }
        #loginOpen .close{
            display: none;
        }
        #forgotPasswordmodal .close{
            display: none;
        }
        .g-blue{ color:#4285F4; }
        .o-red{ color:#DB4437; }
        .o-yellow{ color:#F4B400; }
        .l-green{ color:#0F9D58; }
        .e-red { display:inline-block;transform:rotate(-20deg); }

        .shTab{
            width:70%!important;
        }
        .shTabContent{
            width:60%!important;
        }
        @media (max-width:768px) {
            .shTab,
            .shTabContent{
                width: 100% !important;
            }
            .quizes{
                top: -15px !important;
            }
        }

        .quiz-prev-btn{
            background: #3166C9;
            color: #fff;
            width: auto;
        }
        .quiz-next-btn{
            width: 100px;
            background: #3166C9;
            color: #fff;
        }
        .mob-quiz-header{
            align-items: center;
            justify-content: flex-end;
            gap: 10px;
            margin-top:5px;
        }
        .desktop-quiz-nav{
            align-items: center;
            justify-content: flex-end;
            gap: 10px;
        }
        .btn-submitQuiz{
            border-color:#3166C9 !important;
        }
        .btn-submitQuiz:hover{
            background:#3166C9 !important;
        }
    </style>
    <%if("true".equals(params.fromExternal)){%>
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-Y0QVWCD9K" defer></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-6CJSRG577K');
    </script>
    <style>
    .navbar-header,
    .headerCategoriesMenu,
    header,
    footer,
    .favMcqBtnNav,
    .favStarBtn-1{
        display: none !important;
    }
    </style>
    <%}%>
</head>

<body>
<g:render template="/${session['entryController']}/navheader_new"></g:render>


<!-------- LOADER --------->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
       <div class="loader">Loading</div>
    </div>
</div>


<!-------- MAIN PAGE --------->
<div class="main-page mx-auto" style="display: none">
    <div class="prepjoy-game d-none" id="prepjoy-game">
        <div class="d-flex justify-content-between">
            <div class="mcq-type"><p class='quizTypeName'>Test</p></div>
            <i class="material-icons" onclick="backToHome()">close</i>
        </div>
        <h2 class="mt-4 mcq-name"></h2>
        <div class="instruction-wrapper">
            <div class="row">
                <div class="col-6">
                    <div class="d-flex align-items-center">
                        <div class="set-box mt-2">
                            <i class="material-icons">text_snippet</i>
                        </div>
                        <p class="total-que">20 <br/><span>Questions</span></p>
                    </div>
                </div>
                <div class="col-6">
                    <div class="d-flex align-items-center">
                        <div class="set-box mt-2">
                            <i class="material-icons">thumb_up_alt</i>
                        </div>
                        <p class="mt-2">+1<br/> <span>Correct</span></p>
                    </div>
                </div>
                <div class="col-6 mt-4">
                    <div class="d-flex align-items-center">
                        <div class="set-box mt-2">
                            <i class="material-icons">gpp_bad</i>
                        </div>
                        <p class="mt-2">0 <br/> <span>Incorrect</span></p>
                    </div>
                </div>
                <div class="col-6 mt-4">
                    <div class="d-flex align-items-center">
                        <div class="set-box mt-2">
                            <i class="material-icons">do_disturb</i>
                        </div>
                        <p class="mt-2">0 <br/><span>Skipped</span></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="customize-header lang-module d-flex align-items-center justify-content-between">
                <label>Language:</label>
                <select class="lang form-control" id="languageSelect" onchange="langSelect(this)">
                    <option value="english">English</option>
                    <option value="hindi">Hindi</option>
                </select>


            </div>
            <div class="customize-header time-module mt-4 d-flex align-items-center justify-content-between">
                <label>Time/Question:</label>
                <select class="time-box form-control" id="timeSelect" onchange="timeSelect(this)">
                    <option value="15">15 seconds</option>
                    <option value="30">30 seconds</option>
                    <option value="45">45 seconds</option>
                    <option value="60">60 seconds</option>
                    <option value="75">75 seconds</option>
                    <option value="90">90 seconds</option>
                    <option value="105">105 seconds</option>
                    <option value="120">120 seconds</option>
                </select>
            </div>
        </div>
        <div class="clock-wrapper">
            <div class="clock-header">
                <div class="d-flex justify-content-between">
                    <h4>Change Time</h4>
                    <i class="material-icons" onclick="closeTimer()">close</i>
                </div>
            </div>
            <div class="clock-explanation">
                <div class="form-check">
                    <input class="form-check-input d-none" type="radio" name="flexRadioDefault" value="15" id="flexRadioDefault1" checked>
                    <label class="form-check-label" for="flexRadioDefault1">
                        15 sec
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input d-none" type="radio" name="flexRadioDefault" value="30" id="flexRadioDefault2">
                    <label class="form-check-label" for="flexRadioDefault2">
                        30 sec
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input d-none" type="radio" name="flexRadioDefault" value="60" id="flexRadioDefault3">
                    <label class="form-check-label" for="flexRadioDefault3">
                        60 sec
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input d-none" type="radio" name="flexRadioDefault" value="90" id="flexRadioDefault4">
                    <label class="form-check-label" for="flexRadioDefault4">
                        90 sec
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input d-none" type="radio" name="flexRadioDefault" value="120" id="flexRadioDefault5" >
                    <label class="form-check-label" for="flexRadioDefault5">
                        120 sec
                    </label>
                </div>
            </div>
        </div>
        <div class="inital-btn mt-4 text-center">
            <button class="btn btn-practices" onclick="onStart()">Start</button>

        </div>
    </div>
        <div class="webMcq" id="webMcq">
            <!-------- header ------->
            <div class="webMcq-instruction">
                <div class="container-custom"><br>
                    <h2 class="mcq-name">${title}</h2>
                    <p class="total-que d-flex align-items-center justify-content-center mb-0">10 Questions</p>
                </div>
             </div>

            <!-------- instruction ------->
            <div class="container-custom">
                <div class="instruction-section p-2 p-sm-2 mb-4 mt-3" id="inst-sec" style="display: none">
                    <h4 class="quizhead">Test Instruction</h4><br>
                    <div id="instructionText">

                    </div>
                </div>
                <div class="gen-instruction-section p-2 p-sm-2 mt-3">
                    <div class="qz-wrap">
                        <div class="pointsDetails-cards" style="display:none;">
                            <div>
                                <h3>+1</h3>
                                <h5 class="text-success">Correct Answer</h5>
                            </div>
                            <div>
                                <h3>0</h3>
                                <h5 class="text-danger">Wrong Answer</h5>
                            </div>
                            <div>
                                <h3>0</h3>
                                <h5 class="text-warning">Skip</h5>
                            </div>
                        </div>
                        <!-------- section based quiz ------->
                        <div class="main-page__sectionDetails" style="display: none">
                            <div class="main-page__sectionDetails-description">
                                <p>No. of Sections : <span id="sectionNo"></span></p>
                                <p class="d-none sectotalTimeCount">Total Time : <span id="sectionTotalTime">10 mintues</span> minutes</p>
                            </div>
                            <table class="table table-bordered ">
                                <thead>
                                <tr>
                                    <th scope="col">Section Name</th>
                                    <th scope="col">No. of Question</th>
                                    <th scope="col">Negative Marks</th>
                                    <th scope="col">Positive Marks</th>
                                    <th scope="col">Time(min)</th>
                                </tr>
                                </thead>
                                <tbody id="sectionQuizDetails">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-------- user inputs ------->
            <div class="container-custom p-2">
                <div class="selector-all mb-5">
                    <div class="d-none align-items-center justify-content-between mb-3 gameSettings">
                        <div class="d-flex align-items-center gameTimerSelection">
                            <p class="mb-0 mr-2">Timer</p>
                            <select name="timer" id="gameTimer">
                                <option value="15">15 Secs</option>
                                <option value="30">30 Secs</option>
                                <option value="60">60 Secs</option>
                            </select>
                        </div>

                        <div class="gameSoundToggle d-flex align-items-center">
                            <p class="mb-0">Game Sound</p>
                            <input type="checkbox" id="gameSound">
                            <audio id="audio" src="/funlearn/showImage?id=2&fileName=battle.mp3&imgType=audio"></audio>
                            <label for="gameSound" class="gameSoundToggleBtn">
                                <div class="gameSoundToggleBtnBall"></div>
                            </label>
                        </div>
                    </div>
                    <div class="d-flex align-items-center w-100">
                        <select class="lang form-control w-100" id="languageSelectWeb" onchange="langSelect(this)">
                        </select>
                    </div>

                    <div class="flex-column w-100"  id="noOfQuestionsOption">
                        <div id="radio-12">
                            <input id="radio-1" class="radio-custom first" name="radio-group" type="radio" checked>
                            <label for="radio-1" id="radio-1" class="radio-custom-label">Take all questions</label>
                        </div>
                        <div id="radio-21">
                            <input id="radio-2" class="radio-custom second"name="radio-group" type="radio">
                            <label for="radio-2"  id="radio-2" class="radio-custom-label">Take limited questions</label>
                        </div>
                    </div>
                    <div class="show-select d-none">
                        <div class="inner-container-show-1">
                            <div>
                                <p class="mb-0">Choose the number of questions</p>
                                <input  id="input-b"  class="text-input--1 form-control"   type="number" min="1" oninput="validity.valid||(value='');">
                            </div>
                            <h2 class="text-center">OR</h2>
                            <div>
                                <p class="mb-0">Choose specific range of questions</p>
                                <div class="two-box-container d-flex justify-content-center align-items-center">
                                    <div class="left-box mr-2">
                                        <input  id="left-b" class="text-input--1 form-control" placeholder="From"  type="number" min="1" oninput="validity.valid||(value='');">
                                    </div>
                                    <div class="right-box">
                                        <input  id="right-b" class="text-input--1 form-control" placeholder="To" type="number" min="1" oninput="validity.valid||(value='');" >
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="button-container-selector">
                        </div>

                    </div>
                </div>
            </div>

            <!-------- start button ------->
            <div class="main-page__startButton container-div mb-5">
                <button class="btn main-page__startButton-button btn-outline-secondary cancel" onclick="closePage()">Cancel</button>
                <button class="btn main-page__startButton-button continue" onclick="onStart()">Start</button>
            </div>



        </div>
    </div>
</div>

<!-------- PRACTICE CONTAINER --------->
<div class="container p-0 practice-container d-none">
    <h2 class="title-header d-none">Current Affairs</h2>
    <div class="row align-items-start mx-0 h-100">

        <div class="col-12 col-lg-2 preview-quiz d-lg-block d-none">

            <div class="preview-quiz__header">
                <div class="d-lg-none"><h4><i class="material-icons">visibility</i> Test Preview</h4></div>
                <i class="material-icons d-lg-none" onclick="closePreview()">close</i>

            </div>
            <div id="testTimerSectionWrap">
                <div class="align-items-center svg-timer" id="testTimerSection" style="display: none">
                    <div class="normal-time">
                        <svg id="time-progress" width="35" height="35" viewBox="0 0 200 200" style="transform: rotate(-90deg)">
                            <circle cx="100" cy="100" r="90" stroke="#e0e0e0" stroke-width="20" fill="none"></circle>
                            <circle cx="100" cy="100" r="90" stroke="#76c7c0" stroke-width="20" fill="none" stroke-dasharray="565.48" stroke-dashoffset="565.48"></circle>
                        </svg>

                        <button class="play d-none" id="pause" data-setter=""></button>
                    </div>
                    <div class="sectiontime-wrapper">
                        <p class="timeLeft tot-time-text">Total Time Left</p>
                        <span class="c display-remain-time">00.00</span>
                    </div>
                </div>
            </div>

            <div class="section-selection" id="desktopSelect" style="display:none;">
                <select class="sections form-control" id="sectionSelect" onchange="sectionChange()" style="display: none">

                </select>
            </div>

            <div class="previewTab">
                <div>
                    <p id="sectionDisplayName"></p>
                </div>
                <div id="total-que-tab">
                    <div class="num-wrapper">
                        <div class="num-text">1</div>
                        <div id="attempt-status">Not Attempted</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-7 practice-quiz-wrapper  px-0 px-md-3" style="height: 88%;display: flex;flex-direction: column">
            <!---- HEAER---->
            <div class="app-header">
            <div class="d-lg-none d-flex align-items-center app__header-submit">
                <i class="material-icons d-lg-none" onclick="showPreview()">grid_view</i>
                <!-- PLACE THE testTimerSectionWrap div for mobile -->
                <div id="testTimerSectionWrapMobile" style="display: none;margin-left: auto">
                    <div class="align-items-center svg-timer d-flex" id="testTimerSectionMob" style="gap: 5px">
                        <div class="normal-time">
                            <svg id="time-progressMob" width="25" height="25" viewBox="0 0 200 200" style="transform: rotate(-90deg)">
                                <circle cx="100" cy="100" r="90" stroke="#e0e0e0" stroke-width="20" fill="none"></circle>
                                <circle cx="100" cy="100" r="90" stroke="#76c7c0" stroke-width="20" fill="none" stroke-dasharray="565.48" stroke-dashoffset="565.48"></circle>
                            </svg>
                            <button class="play d-none" id="pause" data-setter=""></button>
                        </div>
                        <div class="sectiontime-wrapper d-flex align-items-center justify-content-between">
                            <p class="timeLeft tot-time-text"></p>
                            <span class="c display-remain-time" style="width: 75px;">00.00</span>
                        </div>
                    </div>
                </div>
                <div id="mobTestTimer" style="display: none">

                </div>
                <div style="margin-top: 5px;position: relative;z-index: 99;" class="ml-auto d-flex align-items-center topSubSec">
                    <img src="${assetPath(src: 'mobile/language1.svg')}" class="fa-language d-none lang1" style="font-size: 2rem;cursor: pointer;margin-right: 10px;" onclick="switchLanguage()">
                    <img src="${assetPath(src: 'mobile/language.svg')}" class="fa-language d-none lang2" style="font-size: 2rem;cursor: pointer;margin-right: 10px;" onclick="switchLanguage()">
                    <button class="btn-submitQuiz-mob ml-auto d-lg-none mr-2" onclick="backToHomeModal()" title="Exit quiz">
                        <i class="fa-solid fa-xmark"></i>
                    </button>
                </div>
            </div>
            <div class="d-lg-none d-flex mob-quiz-header">
                <button class="btn quiz-prev-btn btn-prev-common" id='btn-prev-mobile' onclick="prevQue()" title="go to previous question">Previous</button>
                <button class="btn quiz-next-btn btn-next-common" id="btn-next-mobile" onclick="nextQue()" title="go to next question">Next</button>
                <button class="btn btn-submitQuiz mr-2 d-block d-lg-none" onclick="finish()" id="subNewBtn-1" title="complete test">Submit</button>
            </div>
                <div class="d-flex flex-row-reverse align-items-center justify-content-between app__header-submit-2">
                    <div class="main-timer d-none">
                        <div class="time-wrapper">
                            <p id="test_timer"></p>
                        </div>

                    </div>
                    <div class="d-none d-lg-flex justify-content-center align-items-center">
                        <img src="${assetPath(src: 'mobile/language1.svg')}" class="fa-language d-none lang1" style="font-size: 2rem;cursor: pointer;margin-right: 10px;" onclick="switchLanguage()">
                        <img src="${assetPath(src: 'mobile/language.svg')}" class="fa-language1 d-none lang2" style="font-size: 2rem;cursor: pointer;margin-right: 10px;" onclick="switchLanguage()">
                        <div class="d-none d-lg-flex desktop-quiz-nav">
                            <button class="btn quiz-prev-btn btn-prev-common" id='btn-prev-desktop' onclick="prevQue()" title="go to previous question">Previous</button>
                            <button class="btn quiz-next-btn btn-next-common" id="btn-next-desktop" onclick="nextQue()" title="go to next question">Next</button>
                            <button class="btn btn-submitQuiz mr-2 d-none d-lg-block" onclick="finish()" style="width: 100px" id="subNewBtn-2"  title="complete test">Submit</button>
                        </div>
                        <button class="btn-submitQuiz-mob ml-auto d-none d-lg-block mr-2" onclick="backToHomeModal()" title="Exit quiz">
                            <i class="fa-solid fa-xmark"></i>
                        </button>
                    </div>
                    <i class="material-icons invisible closeIcon" onclick="backToHomeModal()">close</i>
                    <div class="section-selection" id="mobSelect" style="display:none;">

                    </div>
                    <h4 class='quizTypeName mb-0'>Test</h4>
                </div>
                <div class="app-header__questionsQue">
                    <h6>Question <span id="quesList"></span> </h6>
                    <div class="questionProgress"><div class="questionProgress__bar"></div></div>
                </div>
            </div>
            <!--Game Profile-->
            <div class="gamer-profile d-none">
                <div class="circle-wrapper">
                    <div class="roundbg">
                        <img src="/assets/prepJoy/pointer.svg">
                    </div>
                </div>
                <div class="col-12 playerbg">
                    <div class="container">
                        <div class="media">
                            <img src="${assetPath(src: 'prepJoy/profsample.jpeg')}"  class="mr-3 rounded-circle user-profile-image" alt="" width="75px" height="75px">
                            <div class="media-body">
                                <h5 class="mt-0 username playerName rUser">You</h5>
                                <p class="text-white">Novice</p>
                                <p id="userPlace" class="text-white"></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="meridian">
                </div>
                <div class="col-12 botbg botselect">
                    <div class="container">
                        <div class="media botProfile d-none">
                            <div class="media-body">
                                <h5 class="mt-0 username challengerName">Virat Kohli</h5>
                                <p class="text-white">Novice</p>
                                <p id="botPlace" class="text-white">Level 1</p>
                            </div>
                            <img src="${assetPath(src: 'prepJoy/profsample.jpeg')}" class="ml-3 bot-profile-img rounded-circle" alt="" width="75px" height="75px">
                        </div>
                        <h4 class="text-center locate">Locating the user...</h4>
                        <div class="bot-wrapper">
                            <div class="bot-anim-wrapper d-none">
                                <div class="bot-anim"></div>
                                <div class="bot-anim"></div>
                                <div class="bot-anim"></div>
                                <div class="bot-anim"></div>
                            </div>
                            <div class="img-bot"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="no-counter d-none">
                <div id="no-counter"></div>
            </div>


            <!--Players Profile top bar-->
            <div class="gameExit d-none justify-content-end align-items-center">
                <button class="gameExitBtn">Exit Game</button>
            </div>
            <div class="quiz-profile container-fluid d-none">
                <div class="media">
                    <img src="/assets/prepJoy/profsample.jpeg" class="mr-3 rounded-circle user-profile-image" alt="" width="45px" height="45px">
                    <div class="media-body">
                        <h5 class="mt-0 username playerName">MS Dhoni</h5>
                        <p class="score" id="playerScore">0 pts</p>
                    </div>
                </div>
                <div class="main-timer">
                    <h4>Time Left</h4>
                    <div class="time-wrapper">
                        <div id="app">

                        </div>
                        <p id="countdown" class="timer_sec">15</p>
                    </div>

                </div>
                <div class="media">
                    <div class="media-body">
                        <h5 class="mt-0 username challengerName">Virat Kohli</h5>
                        <p class="score" id="botScores">0 pts</p>
                    </div>
                    <img src="/assets/prepJoy/profsample.jpeg"  class="ml-3 rounded-circle bot-profile-img" alt="" width="45px" height="45px">
                </div>
            </div>

            <div class="d-flex justify-content-end mr-3">
                <p class="audioSwitch d-none"><i class="material-icons"></i></p>
            </div>
            <div class="d-none justify-content-center text-center align-items-center displayQuestionNumber mb-4" >
                <div class='question-no text-white'></div>

                <div>

                </div>
            </div>

            <div class="quizes d-none" style="top: -45px;">
                <!--Success Progressbar-->
                <div class="progress progress-bar-vertical player1">
                    <div id='player1' class="progress-bar progress-bar-success active" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="height: 0%;">
                        <span class="sr-only">60% Complete</span>
                    </div>
                </div>
                <!--Question and Answers-->
                <div class="container swiper-container">
                    <p class="audioChange material-icons" onclick="audioChange()"><i class="material-icons">volume_up</i> </p>

                    <div class="question-wrapper">
                        <div class="favQuestionStar">

                        </div>
                        <div class="directions d-none">
                            <p id="direction" class="more">
                                Lorem Ipsum is simply dummy text of  the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.
                            </p>
                        </div>
                        <p class="que_text">What is the manchester of South India?</p>
                        %{--            <img src="${assetPath(src: 'prepjoy/html.jpeg')}" width="100%" height="100px">--}%
                        <button class="btn btn-review d-none"><i class="material-icons">push_pin</i><span> Mark for Review</span></button>
                    </div>
                    <div class="que-options-wrapper mt-4">
                        <div class="que-options">

                        </div>
                        <div class="justify-content-center align-items-center mt-4" style="gap: 1rem">
                            <div class="show-explanation" style="display: none;">

                            </div>
                            <button class="btn show-explanation-1" onclick="showDescription()" style="display: none;">
                                <div class="d-flex justify-content-center align-items-center">
                                    <i class="material-icons mr-2">description</i> Show Explanation
                                </div>
                            </button>
                            <div class="mcqChatBtns d-none align-items-center justify-content-center mt-3">
                                <button class="btn show-video-explanation mb-3" onclick="showVideo()" style="display: none;"><i class="material-icons mr-2">ondemand_video</i> Video Explanation</button>
                                <%if(params.fromgpt!=null){%>
                                <button class="btn d-flex align-items-center mcqChatEx hin mc-primary" onclick="askDoubt('hint','')">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path></svg>
                                    Give Hint
                                </button>
                                <button class="btn d-flex align-items-center mcqChatEx exp mc-secondary" onclick="askDoubt('explain','')">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path></svg>
                                    Explain MCQ
                                </button>
                                <button class="btn d-flex align-items-center mcqChatEx squ mc-tertiary" onclick="askDoubt('similarMCQ','')">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path></svg>
                                    Create similar MCQs
                                </button>
                                <%}%>
                            </div>
                        </div>
                    </div>
                </div>

                <!--Success Progressbar-->
                <div class="progress progress-bar-vertical player2">
                    <div id='player2' class="progress-bar progress-bar-success active" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="height: 0%;">
                        <span class="sr-only">60% Complete</span>
                    </div>
                </div>

            </div>

            <!--User win status Dialog box-->
            <div class="modal fade" id="winnerModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" aria-labelledby="winnerModal" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-dialog-zoom modal-sm" role="document">
                    <div class="modal-content">
                        <div class="modal-header d-none">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div id="winner">
                            </div>
                            <h1 class="user-status">You Won</h1>
                            <div id="submitQuiz"></div>
                        </div>
                        <div class="modal-footer">

                        </div>
                    </div>
                </div>
            </div>

            <div class="modal fade" id="rankModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" aria-labelledby="winnerModal" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-dialog-zoom modal-sm" role="document">
                    <div class="modal-content">

                        <div class="modal-body medal-wrappers">
                            <div>
                                <div id="rank-upload"></div>
                                <h1>Congratulations</h1>
                                <p id="rank-message">You have got 1st Rank</p>
                                <div id="rank-cup" class=''>
                                </div>
                                <button class="btn btn-answer mt-3" data-dismiss="modal">Back to Scoreboard</button>
                            </div>

                        </div>

                    </div>
                </div>
            </div>

            <div class="modal fade" id="continue-test" data-keyboard="false" data-backdrop="static">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">

                        <!-- Modal body -->
                        <div class="modal-body" id="temp-mod">
                            <div class="content-wrapper">
                                <h4 class="timeup text-center">Time's Up!</h4>
                                <p id="completed-subject" class="text-center"> The time to complete this section has ended.</p>
                                <p class="comingup text-center">COMING UP - <span id="comingup-subject"></span></p>
                            </div>
                        </div>

                        <!-- Modal footer -->
                        <div class="modal-footer">
                            <button onclick="javascript: nextSection();" data-dismiss="modal" class="btn btn-continue">Continue</button>
                            %{--<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>--}%
                        </div>

                    </div>
                </div>
            </div>
            <div class="modal fade" id="force-submit-test" data-keyboard="false" data-backdrop="static">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <!-- Modal body -->
                        <div class="modal-body text-center" id="submitTest">
                            <h1 class="submit">Submit ?</h1>
                            <p>Your test will be submitted now. </p>
                        </div>

                        <!-- Modal footer -->
                        <div class="modal-footer">
                            <button onclick="javascript:submitFromModal();" data-dismiss="modal" class="btn submit">SUBMIT</button>

                        </div>

                    </div>
                </div>
            </div>
            <!--Score Dashboard-->
            <div class="resultWrapper d-none">
                <div class="container-fluid">
                    <div class="d-flex justify-content-between result-header">
                        <h2 onclick="exitTest()"><i class="material-icons">
                            arrow_left
                        </i></h2>
                        <p><span class="result">Results</span></p>
                        <p class="invisible" id="sharebtn" onclick='showShareIcon()'><i class="material-icons">share</i> Share</p>
                    </div>
                    <div class="mt-3">


                        <div class="badge-screen d-none">
                            <div class="text-center">
                                <h1 id="congrats" class="">
                                    Congratulations!
                                </h1>
                                <p>You’ve been promoted to</p>
                                <div class="badge">

                                </div>
                                <p id="badgetype"></p>
                            </div>
                        </div>

                        %{--<img src="/assets/prepjoy/celebration.gif"/>--}%
                        <div class="score-media d-flex justify-content-center align-items-center">
                            <div class="playScoreCard">
                                <p class="score-text pb-0 playScoreCard__text">You scored</p>
                                <p id="score" class="updateScore playScoreCard__points">123</p>
                            </div>
                        </div>
                        <h4 id="user-status" class="pb-2 result-status-text">
                            You Won!
                        </h4>
                        <div id="slider" class="slider">
                            <div class="custom-handle ui-slider-handle"></div>
                        </div>
                        <div class="container">
                            <div class="row justify-content-between badges">
                                <div>
                                    <p class="currentBadge">pCommander</p>
                                    <p  class="currentBadgePoints">2000 pts</p>
                                </div>
                                <div class="">
                                    <p class="nextBadge"> pSupreme</p>
                                    <p class="nextBadgePoints">4000 pts</p>
                                </div>
                            </div>

                        </div>
                        <div class="mt-3">
                            <div class="balance-points">You need 500 pts more to promote to pSupreme</div>
                        </div>

                        <div class="button-results">
                            <button class="btn btn-playagain" onclick="showAction('rematch')">Rematch</button>
                            <button class="btn btn-prev btn-playagain" onclick="showAction('previousMatch')">Play Previous</button>
                            <button class="btn btn-next btn-playagain" onclick="showAction('nextMatch')">Play Next</button>
                        </div>

                        <div class="next-match text-center mt-4">
                            <button class="btn btn-outline-secondary btn-answer mx-2" id="practice-summary-result" onclick="showSummary()">Show Answers</button>
                            <button class="btn btn-answer btn-info mx-2 mb-0" id="retryBtn-result" onclick="showAction('rematch')">Retry</button>
                        </div>

                        <div class="answer-wrapper">
                            <div class="answer-indicator">
                                <div>
                                    <div class="box green"></div>
                                    <p>Correct <br/> Answers</p>
                                </div>
                                <div>
                                    <div class="box red"></div>
                                    <p>Incorrect <br/> Answers</p>
                                </div>

                            </div>
                            <div class="qa" id="qa-answer">

                            </div>
                        </div>
                        <div id="lottie"></div>
                    </div>
                </div>
            </div>

            <!----- Bottom sheet for retry option ----->
            <div class="action-wrapper">

                <div class="acw-header">
                    <i class="material-icons close-buttons"  onclick="closeButtons()">close</i>
                </div>
                <div class="action-explanation">
                    <div class="button-results">
                        <button id='btn-play' class="btn btn-playagain" onclick="playQuizAgain()">Play</button>
                        <button id='btn-practice' class="btn btn-playagain" onclick="previousQuiz()">Practice</button>
                        <button id='btn-test' class="btn btn-playagain" onclick="nextQuiz()">Test</button>
                        <button id='btn-learn' class="btn btn-playagain" onclick="learnQuiz()">Learn</button>
                    </div>
                </div>
            </div>

            <!----- Previous, Next button inside quiz ----->
            <div class="app-footer">
                <div class='question-no d-none'></div>
            </div>

            <!----- Explanation slider ----->
            <div class="explanation-wrapper">
                <div class="explanation-header">
                    <div class="d-flex justify-content-between">
                        <h4>Correct Answer:</h4>
                        <i class="material-icons" onclick="closeExplanation()">close</i>
                    </div>
                    <p class="correct-answer" id="correct-answer">test</p>
                </div>
                <div class="answer-explanation">
                    <h4>Explanation:</h4>
                    <div id="explanation"></div>
                </div>
            </div>

            <!----- Video Explanation slider ----->
            <div class="video-explanation-wrapper">
                <div class="explanation-header">
                    <div class="d-flex justify-content-between">
                        <h4>Video Explanation:</h4>
                        <i class="material-icons" onclick="closeVideoExplanationQuiz()">close</i>
                    </div>
                </div>
                <div class="answer-explanation">
                    <div id="videoExplanation"></div>
                </div>
            </div>

        </div>
    </div>
</div>

<div class="practice-result">
    <g:render template="/prepjoy/quizResult"></g:render>
</div>

<g:render template="/prompt/chatModule"></g:render>




<!--------  REPORT MODAL --------->
<div class='modal fade' id='prep-report-question'>
    <div class='modal-dialog modal-dialog-centered modal-dialog-zoom modal-sm'>
        <div class='modal-content prep-report-modal '>
            <div class='modal-header d-flex'>
                <p class='modal-title d-flex align-items-center prep-report-icon'>Report the question <i class='ml-1 material-icons'>error</i> </p>
                <button type='button' class='close' data-dismiss='modal'>&times;</button>
            </div>
            <div class='modal-body d-flex flex-column'>
                <div class="chkbox mt-1">
                    <input type="checkbox" class="form-check-input" id="spellingMistake">
                    <label class="form-check-label tick" for="spellingMistake">
                        <span></span>Spelling Mistake
                    </label>
                </div>
                <div class="chkbox mt-1">
                    <input type="checkbox" class="form-check-input" id="directionNotGiven">
                    <label class="form-check-label tick" for="directionNotGiven">
                        <span></span>Direction not given
                    </label>
                </div>
                <div class="chkbox mt-1">
                    <input type="checkbox" class="form-check-input" id="imageNotVisible">
                    <label class="form-check-label tick" for="imageNotVisible">
                        <span></span>Graph / Image not visible
                    </label>
                </div>

                <div class="chkbox mt-1">
                    <input type="checkbox" class="form-check-input" id="incompleQuestion">
                    <label class="form-check-label tick" for="incompleQuestion">
                        <span></span>Incomplete question
                    </label>
                </div>
                <div class="chkbox mt-1">
                    <input type="checkbox" class="form-check-input" id="otherIssues">
                    <label class="form-check-label tick" for="otherIssues">
                        <span></span>Other Issues
                    </label>
                </div>

                <div class="form-group mt-3 textbox">
                    <textarea class="form-control" id="moreInformation" placeholder="Let us know"></textarea>
                </div>
                <div class="text-center issueErr" style="display: none">
                    <p class="text-danger">Please select any issue.</p>
                </div>
            </div>
            <div class='modal-footer d-flex justify-content-center'>
                <button type='button' class='btn btn-primary btn-submit' onclick="reportSubmit()">Submit</button>
            </div>
        </div>
    </div>
</div>

<!-------- REVIEW QUES MODAL --------->
<div class='modal fade' id='review-modalDialog' data-keyboard="false" data-backdrop="static">
    <div class='modal-dialog modal-dialog-centered modal-dialog-zoom'>
        <div class='modal-content prep-report-modal'>
            <div class='modal-header text-center'>
                <h4>Test Summary</h4>
                <!--                <button type='button' class='close' data-dismiss='modal'>&times;</button>-->
            </div>
            <div class='modal-body d-flex align-items-center flex-column'>
                <span id="noOfQuestions">Total Questions:<p></p></span>
                <div class="d-flex w-100 submitreviewModal">
                    <div class="review-wrap">
                        <div class="box green">
                            <p id='noOfAnswered'></p>
                        </div>
                        <p class="text-quote">Answered</p>
                    </div>
                    <div class="review-wrap review-wrap-submission d-none">
                        <div class="box yellow">
                            <p id='noOfreviewed'></p>
                        </div>
                        <p class="text-quote">Marked for Review</p>
                    </div>
                    <div class="review-wrap">
                        <div class="box blue">
                            <p id='noOfSkipped'></p>
                        </div>
                        <p class="text-quote">Skipped</p>
                    </div>
                </div>


            </div>
            <div class='modal-footer'>
                <button type="button" class="btn btn-default btn-outline-secondary mr-3" data-dismiss="modal">Resume</button>
                <button type='button' class='btn btn-submit mb-0'
                        onclick="submitFromModal()">Submit
                </button>
            </div>
            <div class="total-time-wrapper text-right" style="display:none;">
                <p class="timeLeft">Total Time Left</p>
                <p class="total-test-time totalTimeLeft">00.00</p>
            </div>
        </div>
    </div>
</div>

<!--------  CLOSING TEST MODAL --------->
<div class="modal fade" id="closeTest" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">

            <div class="modal-body">
                Do you want to end the test ?
            </div>
            <div class="modal-footer text-center">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Resume</button>
                <button type="button" class="btn btn-danger"  onclick="exitTest()">Exit</button>
            </div>
        </div>
    </div>
</div>

<!--------  REPORT SUCCESS MODAL --------->
<div class="modal fade report-success__modal modal-modifier" id="report-success">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close mr-2" data-dismiss="modal" aria-label="Close" style="text-align: end">
                <span aria-hidden="true">x</span>
            </button>

            <div class="modal-body modal-body-modifier text-center">
                <div id="addedIconAnimation" class="scaleAnimation">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <h5 class="mt-3">Question Reported.</h5>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-shadow col-6 col-md-5 mr-2" data-dismiss="modal" aria-label="Close">OK</button>
                </div>
            </div>

        </div>
    </div>
</div>

<!--------  NEW MEDAL MODAL --------->
<div class="modal fade" id="medalModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" aria-labelledby="winnerModal" aria-hidden="true">
    <div class="modal-dialog newMedalAlign modal-dialog-centered modal-dialog-zoom" role="document">
        <div class="modal-content" style="background: transparent">
            <div class="modal-body medal-wrappers">
                <div class="text-center text-white">
                    <div id="medal-user" class="medal-user">
                    </div>
                    <lottie-player src="https://assets8.lottiefiles.com/packages/lf20_SC3bWlmCAz.json" id="goldMedal" background="transparent"  speed="1"  style="display: none;width: 200px;margin: 0 auto;position: static"  loop  autoplay></lottie-player>
                    <lottie-player src="https://lottie.host/9d710c7a-a860-423d-b1b3-0e25f63da582/lTQP6czVj7.json" id="silverMedal" background="transparent" speed="1" style="display: none;width: 200px;margin: 0 auto;position: static" loop autoplay></lottie-player>
                    <lottie-player src="https://lottie.host/5ae5fca6-5754-4408-ae81-6f22eed9d9dd/9VKnqqmUaO.json" id="bronzeMedal" background="transparent" speed="1" style="display: none;width: 200px;margin: 0 auto;position: static" loop autoplay></lottie-player>
                    <h4 id="medal-name">You've earned Gold Medal</h4>
                    <h5 id="win-message">You’ve won 8 Consecutive times</h5>
                    <button class="btn btn-answer mt-3 medalSummaryBtn" data-dismiss="modal" style="position: relative;z-index: 999999">View Summary</button>
                    <lottie-player src="https://assets2.lottiefiles.com/packages/lf20_OiqxPlq97j.json" id="medalLottieAnim" background="transparent"  speed="1" loop autoplay></lottie-player>
                </div>

            </div>

        </div>
    </div>
</div>

<!--------  NEW BADGE MODAL --------->
<div class="modal fade modal-modifier" id="badgeModal" data-backdrop="static">
    <div class="modal-dialog full-screen-dialog modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-fullscreen-xl modal-content-modifier">
            <div class="modal-body modal-body-modifier text-center">
                <div id="newBadgeDetails">
                    <h2 id="newBadgeName"></h2>
                    <h4 id="nextBadge"></h4>
                    <button class="btn btn-answer mt-3 badgeSummaryBtn" data-dismiss="modal" style="position: relative;z-index: 999999">View Summary</button>
                    <lottie-player src="https://assets2.lottiefiles.com/packages/lf20_OiqxPlq97j.json" id="badgeLottieAnim" background="transparent"  speed="1" loop  autoplay></lottie-player>
                </div>
            </div>
        </div>
    </div>
</div>
<!--------  NEW RANK MODAL --------->
<div class="modal fade modal-modifier" id="rankUpdateModal" data-backdrop="static">
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">
            <div class="modal-body modal-body-modifier text-center">
                <div id="rankDetails">
                    <h2 id="rankDisplayText">Congratulations on reaching number 4 on All India monthly leaderboard.</h2>
                    <button class="btn btn-answer mt-3 rankSummaryBtn" data-dismiss="modal" style="position: relative;z-index: 999999">View Summary</button>
                    <lottie-player src="https://assets2.lottiefiles.com/packages/lf20_OiqxPlq97j.json" background="transparent"  speed="1" loop  autoplay></lottie-player>
                </div>
            </div>
        </div>
    </div>
</div>
<!--------  NEW INST RANK MODAL --------->
<div class="modal fade modal-modifier" id="rankInstUpdateModal" data-backdrop="static">
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">
            <div class="modal-body modal-body-modifier text-center">
                <div id="rankInstDetails">
                    <h2 id="rankInstDisplayText">Congratulations on reaching number 4 on your institute's monthly leaderboard.</h2>
                    <button class="btn btn-answer mt-3 rankInstSummaryBtn" data-dismiss="modal" style="position: relative;z-index: 999999">View Summary</button>
                    <lottie-player src="https://assets2.lottiefiles.com/packages/lf20_OiqxPlq97j.json" background="transparent"  speed="1" loop  autoplay></lottie-player>
                </div>
            </div>
        </div>
    </div>
</div>

<!-------- REVIEW QUES MODAL --------->
<div class='modal fade' id='rechargeModal' style="z-index: 99999 !important;" data-keyboard="false" data-backdrop="static">
    <div class='modal-dialog modal-dialog-centered modal-dialog-zoom'>
        <div class='modal-content prep-report-modal'>
            <div class='modal-body d-flex align-items-center flex-column'>
                <h5 style="color: #999;text-align: center;line-height: 35px;">Oops! Looks like you're out of tokens. Recharge now to keep the conversation going.</h5>
            </div>
            <div class='modal-footer'>
                <button type="button" class="btn btn-default btn-outline-secondary mr-3 w-100" data-dismiss="modal">Cancel</button>
                <a href="/whitelabel/recharge" class='btn btn-submit mb-0 w-100 text-white' style="background: #F79420;" target="_blank">Recharge</a>
            </div>
        </div>
    </div>
</div>

<!-------- MOCK TEST ALREADY TAKEN MODAL --------->
<div class='modal fade' id='mockTestAlreadyTakenModal' style="z-index: 99999 !important;" data-keyboard="false" data-backdrop="static">
    <div class='modal-dialog modal-dialog-centered modal-dialog-zoom'>
        <div class='modal-content prep-report-modal'>
            <div class='modal-header text-center'>
                <h4>Mock Test Already Taken</h4>
            </div>
            <div class='modal-body d-flex align-items-center flex-column'>
                <h5 style="color: #999;text-align: center;line-height: 35px;">You have already attempted this mock test. Each mock test can only be taken once.</h5>
            </div>
            <div class='modal-footer'>
                <button type="button" class="btn btn-default btn-outline-secondary w-100" onclick="goBackToHome()">OK</button>
            </div>
        </div>
    </div>
</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<%if("true".equals(session["prepjoySite"])){%>
<g:render template="/prepjoy/prepjoy-loader"></g:render>
<% } %>
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.0.0/dist/chart.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
<script>

    var option_tag='';
    var currentUsername='';
    var myQuizId='';
    var resourcesId='';
    var questions=[];
    var challengerName='';
    var challengerPlace='';
    var botName='';
    var source='web';
    // var time_line = document.querySelector(".time_line");
    var timeCount = document.querySelector(".timer_sec");
    var option_list = document.querySelector(".que-options");
    var que_count = 0;
    var que_numb = 1;
    var timeValue =  1500;//Change time here will change in all places(All answer progressbar logic wil work fine);
    var questionTime=timeValue/100;
    var userScore = 0;
    var botScore=0;
    var counterLine;
    var widthValue = 0;
    var counter;
    var globalTimer;
    var checkbotAnswer;
    var botAnswerTime;
    var chatBotAnswerTime;
    var myCounter;
    var botAnswerFirst=false;
    var userAnswerFirst=false;
    var userAnswers=[];
    var botAnswer;
    var qaObj={
        userAnswers:[],
    };
    var userTotalTime=0;
    var botTotalTime='';
    var correctOption=-1;
    var que_answer;
    var userId='';
    var qIndex;
    var newTime;
    var userImage='';
    var language='';//for web
    var userPlace='';
    var submitUrl='';
    var sound;
    var countdown;
    var span;
    var timeObj;
    var tt;
    var quizMode=''; // Can be Practice,Testseries,
    var url = new URL(window.location.href);
    var baseUrl = url.origin+'/';
    var previous='yes';
    var next='no';
    var practicePrevious=false;
    var monthly='no';
    var weekly='no';
    var quizType='';
    var noOfQuestions='';
    var dailyTest=false;
    var resourceDate='';
    var fromShop='';
    var siteId = "${session['siteId']}";
    var explainLink = ""; // for web
    var storingQaForWeb; //for storing questions for web to play rematch
    var learn = ${params.learn};
    var resourceName;
    var siteName = '${params.siteName}';
    var totalNoOfQues;
    var fromQueNo;
    var toQueNo;
    var defaultQueNo;
    var quesList;
    var isAppend = false;
    var fromPubDesk = "${params.pubDesk}";
    var sectionPresent=false;
    var sectionDtl;
    var sectionMst;
    var sectionLength;
    var sectionTotalTime;
    var subjectsList=[];
    var questionsList;
    var sectionSelectValue;
    var sectionQuestions=[];
    var storingInitialData;
    var qSubject;
    var testGenId;
    var testGen=false;
    var chpList = "${params.chaptersList}";
    var TestGenBook = "${params.TestGenBook}";
    var chapterId = "";
    var reviewedQ=false;
    var finalList;
    var realDailyTestDtlId;
    var testId;
    var gameSound=false;
    var parentQuizRecId;
    var questionOptions;
    var quizRecIdVal;
    var questionOptionsList;
    var bookLevel,bookSyllabus,bookGrade,bookSubject;
    var language1="";
    var language2="";
    var exitQuiz=false;
    var languageToggleSwitch=false;
    var quizStatisticsList = [];

    //Game sound functionality
    var gameBackgroundMusic;
    var questionDisplay;
    var botCorrect;
    var botIncorrect;
    var userCorrect;
    var userIncorrect;
    var lose;
    var win;
    var tie;
    var playStart;
    var opponentFound;
    var opponentScroll;
    var timeUp;
    var botAnsweredQues = false;
    var userlistId= "";
    var favouriteQ=false;
    var prepjoySite = "${session['prepjoySite']}";
    var secTimerPresent=false;
    let ibookgpt
    var isFromGPT= false;
    var hasLibraryAccess = false;
    var gpt = "${enableai}";
    var readId = "${params.readId}" || "${params.resId}"
    let freeTokenCount = 0;
    let paidTokenCount = 0;
    let bookId = "${bookId}";
    let mcqTotalTime = null;
    let fromLiveMockTest = "${params.fromLiveMockTest}";
    var showTestResults = true; // Default to true, will be updated from response
    var testResultDate = null; // Will be updated from response
    var ranks = []
    var quizResName = ""

    <%if(showLibrary){%>
        hasLibraryAccess = true;
    <%}%>

    var isFromExternal = false;

    <%if("true".equals(params.fromExternal)){%>
        isFromExternal = true;
    <%}%>

    if(siteId=="71" && gpt == "true" && (bookId && bookId.trim()!="")){
        async function getTokenDetails(){
            const response = await fetch("/prompt/getTokenDetails?bookId="+bookId)
            if(!response.ok){
                return;
            }
            const result = await response.json()
            if(result.error){
                return;
            }
            freeTokenCount = result.freeTokenCount
            paidTokenCount = result.paidTokenCount
        }
        getTokenDetails()
    }
    //ONLY FOR APP
    function changeTestTime(time){
        timeValue=time*100;
        questionTime=timeValue/100;
        TIME_LIMIT = timeValue/100;
        $('#countdown').text(questionTime);
        if(timeValue>6000){
            $('#countdown').text('00.00');
        }
    }

    //FOR WEB
        if (chpList == "" && chpList == null && chpList == undefined && '${params.historyPage}' !='true'){
            $('.main-page').show();
        }

    if(source =='web') {
        <sec:ifLoggedIn>
        currentUsername = "${session['userdetails'].name}";
        $('.playerName').text(currentUsername);
        userId="${session['userdetails'].username}";
        userId = userId.replace("&#64;","@");
        userlistId = "${session['userdetails'].id}";
        </sec:ifLoggedIn>
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }

        <%if("bookTest".equals(params.mode)){%>
            testGen=true; //API CALL FOR TEST GENERATOR
            <g:remoteFunction controller="testgenerator" action="getTestDetails" params="'testId=${params.testId}'" onSuccess = "initializeQuizResults(data);"/>
        <%}else{%>
            <%if("false".equals(params.dailyTest)){%>
                //API CALL FOR NORMAL QUIZ
        <%if(params.noOfQuestions!=null){%>
                isFromGPT=true;
                <g:remoteFunction controller="funlearn" action="newQuizQA" params="'quizId='+${params.quizId}+'&resId='+${params.resId}+'&siteId='+siteId+'&noOfQuestions=${params.noOfQuestions}&difficultyLevel=${params.difficultyLevel}&fromLiveMockTest=${params.fromLiveMockTest}'" onSuccess = "initializeQuizResults(data);"/>
         <%}else{%>
            <g:remoteFunction controller="funlearn" action="newQuizQA" params="'quizId='+${params.quizId}+'&resId='+${params.resId}+'&siteId='+siteId+'&fromLiveMockTest=${params.fromLiveMockTest}'" onSuccess = "initializeQuizResults(data);"/>
        <%}%>
        <%}else if("true".equals(params.historyPage)){%>
                //API CALL FOR HISTORY
                <g:remoteFunction controller="prepjoy" action="getFullQuizDetails" params="'siteId='+siteId+'&quizRecId='+${params.quizRecId}" onSuccess="showAllData(data)" />
            <%}else if("retest".equals(params.mode)  ){%>
               //API for retest
                 <g:remoteFunction controller="prepjoy" action="getRetestDetails" params="'siteId='+siteId+'&parentQuizRecId=${params.parentQuizRecId}&questionOptions=${params.questionOptions}'" onSuccess = "initializeQuizResults(data);" />
            <%}else if("improve".equals(params.mode)  ){%>
            //API for Current Affairs
                <g:remoteFunction controller="prepjoy" action="getImproveQuestionsForMCQs" params="'siteId='+siteId+'&subject=${params.subject}'" onSuccess = "initializeQuizResults(data);" />
            <%}else if("caDaily".equals(params.mode)  ){%>
                <g:remoteFunction controller="funlearn" action="quizQuestionAnswers" params="'currentAffairsType=Main&resId='+${params.resId}+'&siteId='+siteId" onSuccess="initializeQuizResults(data)" />
            <%}else if("caWeekly".equals(params.mode) || "caMonthly".equals(params.mode)){%>
                var caParams = 'noOfQuestions='+'${params.noOfQ}'+'&currentAffairsType=Main&dateInput='+'${params.dateInput}'+'&siteId='+siteId+'&noOfDays='+'${params.days}';
                <g:remoteFunction controller="prepjoy" action="getMultiDaysQuiz" params="caParams" onSuccess="initializeQuizResults(data)" />
            <%}else{%>
                //API CALL FOR DAILY TEST (DAILY TEST 4TH API)
                var dailytestID = '${params.dailyTestId}';
                var dateInput = '${params.dateInput}';
                <g:remoteFunction controller="prepjoy" action="getDailyTests" params="'dailyTestId='+dailytestID+'&dateInput='+dateInput+'&siteId='+siteId" onSuccess="initializeQuizResults(data)" />
            <%}%>
        <%}%>
    }

    function shuffle(o){
        for(var j, x, i = o.length; i; j = Math.floor(Math.random() * i), x = o[--i], o[i] = o[j], o[j] = x);
        return o;
    }

    //App will come here directly
    function initializeQuizResults(data) {
        if(data.status=="OK"){
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
        storingInitialData=data;
        quesList =  data;
        resourceName = data.resourceName;
        mcqTotalTime = parseInt(data.mcqTotalTime)
        if(isFromGPT){
            document.querySelector('.selector-all').classList.add('d-none');
        }
        var lanStr=""
        if (data.language1!=null && data.language1!="" && data.language1!='null' ){
            lanStr += "<option value='"+data.language1+"'>"+data.language1+"</option>";
        }
        if (data.language2!=null && data.language2!="" && data.language2!='null' ){
            lanStr += "<option value='"+data.language2+"'>"+data.language2+"</option>";
        }
        if (data.language3!=null && data.language3!="" && data.language3!='null' ){
            lanStr += "<option value='"+data.language3+"'>"+data.language3+"</option>";
        }

        if ((data.language1==null || data.language1=="") && (data.language2==null || data.language2=="")){
            lanStr += "<option value='English'>English</option>";
        }

        document.getElementById("languageSelectWeb").innerHTML = lanStr;
        language1 = data.language1;
        language2 = data.language2;

        <%if("bookTest".equals(params.mode)){%>
        questions = data.results;
        storingQaForWeb = data.results;
        quizMode='${params.quizType}';
        testGenId='${params.testId}';
        myQuizId=-1;
        resourcesId = -1;
        chapterId = -1;
        console.log("test duration is "+data.duration);
        if(data.duration!=null){
            // Check if we're on mobile and target the appropriate timer
            if (window.innerWidth < 992) {
                // On mobile, use the mobile timer
                $("#testTimerSectionMob").removeAttr('style').addClass('d-flex').attr('style','gap:12px');
            } else {
                // On desktop, use the desktop timer
                $("#testTimerSection").removeAttr('style').addClass('d-flex').attr('style','gap:12px');
            }
            getTimerDt(); // Initialize timer elements before using them
            changeWholeTime(data.duration);
            startTimerForTest();
        }

        <%}else{%>
            <%if("false".equals(params.dailyTest)){%>
                questions = JSON.parse(data.results);
                storingQaForWeb = JSON.parse(data.results);
                myQuizId=${params.quizId};
                resourcesId=${params.resId};
                quizMode='${params.quizType}';
                chapterId = questions[0].chapterId;
            <%}else if("retest".equals(params.mode)){%>

                questions = JSON.parse(data.results);
                storingQaForWeb = JSON.parse(data.results);
                quizMode='${params.quizType}';
                myQuizId=questions[0].id;
                parentQuizRecId ="${params.parentQuizRecId}";
                questionOptions ="${params.questionOptions}";

                <%}else if("improve".equals(params.mode)){%>

                questions = JSON.parse(data.results);
                storingQaForWeb = JSON.parse(data.results);
                quizMode='${params.quizType}';
                myQuizId=questions[0].id;
            <%}else if("caDaily".equals(params.mode)){%>
                questions = data.results;
                storingQaForWeb = data.results;
                myQuizId=questions[0].id;
                resourcesId='${params.resId}';
                quizMode='${params.quizType}';
            <%}else if("caWeekly".equals(params.mode) || "caMonthly".equals(params.mode)){%>
                questions = JSON.parse(data.results);
                storingQaForWeb = data.results;
                myQuizId=questions[0].id;
                quizMode='${params.quizType}';
                '${params.mode}' == 'caWeekly'?weekly='yes':monthly='yes';
            <%}else{%>
                dailyTest=true;
                questions = JSON.parse(data.results);
                storingQaForWeb = JSON.parse(data.results);
                quizMode='${params.quizType}';
                myQuizId=questions[0].id;
                realDailyTestDtlId = data.realDailyTestDtlId;
                testId = dailytestID;
            <%}%>
        <%}%>

        if(quizMode==''){
            quizMode='';
            quizType='play';
        }
        else if(quizMode=='practice'){
            quizMode='practice';
            quizType='practice';
        }
        else if(quizMode=='testSeries'){
            quizMode='testSeries';
            quizType='test';
        }
        else if(quizMode='test'){
            quizMode='test';
            quizType='test';
        }

        if (source == 'android') {
            submitUrl = data.baseUrl;
            weekly=data.weekly;
            monthly=data.monthly;
            tokenId = data.xauth;
            resourceDate=data.resourceDate;
            if (data.nextchallengerPlace != '' && data.nextChallenger != '') {
                challengerName = data.nextChallenger;
                challengerPlace = data.nextchallengerPlace;
            } else {
                challengerName = data.quizData.challengerName;
                challengerPlace = data.quizData.challengerPlace;
            }
            data = data.quizData;
            questions = data.results;
        } else {
            challengerName = data.challengerName;
            challengerPlace = data.challengerPlace;
        }

        if(data.examDtl!=""  && data.examDtl!=null){
            if (window.innerWidth < 992) {
                const secDrop = '<select class="sections form-control" id="sectionSelect" onchange="sectionChange()"></select>'
                $("#mobSelect").html(secDrop)
                $("#mobSelect").show();
                $("#desktopSelect").html('')
                $(".closeIcon").addClass('d-none');
                // Use the static mobile timer instead of dynamic creation
                $("#testTimerSectionWrapMobile").show()
                $("#testTimerSectionWrap").html('')  // Clear desktop timer content to avoid conflicts
                $(".topSubSec").removeClass('ml-auto')
                $(".app__header-submit").addClass('justify-content-between')
                getTimerDt()
            }else{
                $("#sectionSelect").show();
                $("#desktopSelect").show();
                // Show desktop timer and hide mobile timer
                $("#testTimerSectionWrapMobile").hide()

                // Restore desktop timer content if it was cleared
                if ($("#testTimerSectionWrap").html().trim() === '') {
                    $("#testTimerSectionWrap").html(`
                        <div class="align-items-center svg-timer" id="testTimerSection" style="display: none">
                            <div class="normal-time">
                                <svg id="time-progress" width="35" height="35" viewBox="0 0 200 200" style="transform: rotate(-90deg)">
                                    <circle cx="100" cy="100" r="90" stroke="#e0e0e0" stroke-width="20" fill="none"></circle>
                                    <circle cx="100" cy="100" r="90" stroke="#76c7c0" stroke-width="20" fill="none" stroke-dasharray="565.48" stroke-dashoffset="565.48"></circle>
                                </svg>
                                <button class="play d-none" id="pause" data-setter=""></button>
                            </div>
                            <div class="sectiontime-wrapper">
                                <p class="timeLeft tot-time-text">Total Time Left</p>
                                <span class="c display-remain-time">00.00</span>
                            </div>
                        </div>
                    `);
                }
            }
            $('#webMcq').removeClass('d-none');
            $('.selectErr').empty();
            $('#questionSelectionsTab').addClass('d-none');
           if(quizType=='practice'||quizType=='test')
           $("#noOfQuestionsOption").hide();
            sectionQuizUI(data);
        }

        var pubDeskUrl = window.location.href;

        noOfQuestions=questions.length;
        if(quizMode=='testSeries' || quizMode == 'test'){
            $('.btn-review').removeClass('d-none').addClass('d-flex');
            $('.mrkrTab').removeClass('d-none');
            $(".review-wrap-submission").removeClass('d-none');
            $(".navbar").addClass('d-none');
            $(".headerCategoriesMenu").addClass('d-none');
            $(".menu-main-min-height").addClass('d-none');
        }
        botName = challengerName + "-" + challengerPlace;
        $('.challengerName').text(challengerName);
        $('#botPlace').text(challengerPlace);

        <sec:ifLoggedIn>
        if ("${session['userdetails'].profilepic}" !="" && "${session['userdetails'].profilepic}" !=null){
            userImage = "/funlearn/showProfileImage?id="+"${session['userdetails'].id}"+"&fileName="+"${session['userdetails'].profilepic}"+"&type=user&imgType=passport";
        }else{
            userImage = '/assets/wonderslate/avatar.webp';
        }
        </sec:ifLoggedIn>
        <sec:ifNotLoggedIn>
                userImage = '/assets/wonderslate/avatar.webp';
        </sec:ifNotLoggedIn>

        $(".user-profile-image").attr({"src": userImage});

        var botImage = randomIntFromInterval(1, 20);//Randomise image
        if(source !='web'){
            if(quizMode==''){

                if(source=='android') {
                    $(".bot-profile-img").attr({"src": 'file:///android_asset/quiz/assets/images/prepjoy/' + botImage + '.jpg'});
                }
                else{

                    $(".bot-profile-img").attr({ "src": +botImage+'.jpg' });
                }
            }
        }
        else {
            $('.quizTypeName').text(data.resourceName);
            $(".bot-profile-img").attr({"src": '/assets/prepJoy/' + botImage + '.jpg'});

        }
        if((data.language1=='' && data.language2=='') || (data.language1==null && data.language2==null)){
            language='';
            $('.lang-module').removeClass('d-flex').addClass('d-none');
        }
        else if((data.language1!='' && data.language2!='') || (data.language1!=null && data.language2!=null)){
            language=data.language1.toLowerCase();
            $('.lang-module').removeClass('d-none').addClass('d-flex');
        }if((data.language1!='' && data.language2=='') || (data.language1!=null && data.language2==null)){
            language='';
            $('.lang-module').removeClass('d-flex').addClass('d-none');
        }

        if(data.explainLink !='' && data.explainLink != "null" && data.explainLink != null ){
            explainLink = data.explainLink;
            $('.que_video_explain').show();
        }
        if((quizMode !='practice') && (quizMode !='testSeries')){
            $('.app-header,.app-footer').hide();
        }

        if (learn){
            $('#subNewBtn-1').text('Finish');
            $('#subNewBtn-2').text('Finish');
        }
        if(quizMode=='') {
            document.querySelector('.quizes').removeAttribute('style');
            $('body,html').css('background-color','#060029 !important');
            $('.mcq-name,.pointsDetails-cards h3,.total-que,.locate,.analysis h2,.nums,.playerName,.score,.challengerName,.audioSwitch i').addClass('text-white');
            $('#practice-summary,.reviseBtn').addClass('text-white');
            $('.ws_result-summary').css('background-color','#060029 !important');
            $('.ws_result-summary h2').addClass('text-white');
            $('#goBack').addClass('text-white');
            $('.ws_result-summary .fa-circle-xmark').addClass('text-white');
            $('.displayQuestionNumber').removeClass('d-none').addClass('d-flex');
            $('.nav-tabs').css('background','transparent!important');
            $('.nav-tabs li a').css('color','#fff !important');
            $('#btn-play').addClass('d-flex justify-content-center');
            $('.main-page').show();
            $('.gameSettings').removeClass('d-none').addClass('d-flex');
            $('.total-que').html(questions.length + '<br/><span>Questions</span>');
        } else if(quizMode=='test'){
            $('.main-page').show();
            $('.mcq-type').html('<p>Test</p>');
            $('.total-que').html(questions.length + '<br/><span>Questions</span>');
            $('.first-header h3').html(questions.length );
        }
        else if((quizMode =='practice')||(quizMode =='testSeries') || (quizMode=='test')){
            $('.progress').hide();
            $('body,html').css('background-color','#F4F5FA !important');
            $('.total-que').html(questions.length + '<br/><span>Questions</span>');
            $('.first-header h3').html(questions.length);
            $('.main-page').show();
            $('.custom-check:last-child').hide();
            $('.time-module').removeClass('d-flex').addClass('d-none');
            $('#btn-play').addClass('d-flex justify-content-center');
        }else{

        }

        if (sectionPresent){
            questions.sort(arrangeSection);
        }
        if (testGen){
            onStart()
        }

        <%if(params.fromgpt!=null){%>
        ibookgpt = initIbookgpt({
            chatEndPoint:"/prompt/quizGptInteraction", // /api/retrieveData
            docType:"mcq", // "pdf" "txt", "mcq", "epub"
            enableHistory:false,
            enableToken:false,
            enableSnip:false,
            questions:questions
        })
        <%}%>
    }else{
            alert(data.status);
        }
    }


    function randomizeOptionsAndUpdateAnswers(tempQuestions) {
        console.log(tempQuestions);
        return tempQuestions.map(function (question) {
            var options = ["op1", "op2", "op3", "op4"];
            if (question.op5 && question.op5.trim()) {
                options.push("op5");
            }

            var optionsValues = options.map(function (opt) {
                return question[opt];
            });

            optionsValues.sort(function () {
                return Math.random() - 0.5;
            });

            var correctAnswerKey = options.find(function (opt) {
                return question["ans" + opt.slice(2)] === "Yes";
            });

            var correctAnswerIndex = optionsValues.indexOf(question[correctAnswerKey]);

            options.forEach(function (opt) {
                delete question["ans" + opt.slice(2)];
            });

            options.forEach(function (opt, index) {
                question[opt] = optionsValues[index];
                if (index === correctAnswerIndex) {
                    question["ans" + opt.slice(2)] = "Yes";
                }
            });
            return question;
        });
    }

    function checkAvailability(arr, val) {
        return arr.some(arrVal => val === arrVal.subject);
    }
    function sectionQuizUI(data){
        sectionDtl = JSON.parse(data.examDtl);
        sectionMst = data.examMst;
        sectionLength = sectionDtl.length;
        sectionTotalTime = sectionMst.totalTime;
        if (sectionMst.examInstructions !="" && sectionMst.examInstructions!=null){
            if(quizType!='play')  $("#inst-sec").show();
           document.getElementById("instructionText").innerHTML = sectionMst.examInstructions;
        }
        if(quizType!='play')
        $(".main-page__sectionDetails").show();

        $("#sectionNo").text(sectionLength);
        $("#sectionTotalTime").text(sectionTotalTime);
        $(".sectotalTimeCount").removeClass('d-none')

        var optionHtml="";
        var sectionDetailsTable="";
        if (sectionDtl!="" && sectionDtl.length>0 && sectionDtl!=""){
            sectionPresent=true;
            secpresent = true;
            for(var s=0;s<sectionDtl.length;s++){
                subjectsList.push(sectionDtl[s].subject);
                sectionDetailsTable += "<tr>"+
                    " <th scope='col'>"+sectionDtl[s].subject+"</th>"+
                    "<th scope='col'>"+sectionDtl[s].noOfQuestions+"</th>"+
                    "<th scope='col'>"+sectionDtl[s].wrongAnswerMarks+"</th>"+
                    "<th scope='col'>"+sectionDtl[s].rightAnswerMarks+"</th>"+
                    "<th scope='col'>"+sectionDtl[s].totalTime+"</th>"+
                    "</tr>";
            }
            subjectsList.sort();
            document.getElementById("sectionQuizDetails").innerHTML = sectionDetailsTable;
        }
        if(quizType=='test') {
        //   $('.quizes').removeClass('d-flex').addClass('d-none');
            // Check if we're on mobile and target the appropriate timer
            if (window.innerWidth < 992) {
                // On mobile, use the mobile timer
                $("#testTimerSectionMob").removeAttr('style').addClass('d-flex').attr('style','gap:12px')
            } else {
                // On desktop, use the desktop timer
                $("#testTimerSection").removeAttr('style').addClass('d-flex').attr('style','gap:12px')
            }
            if (sectionMst != null && !sectionMst == "") {
                examMst = sectionMst;
                if (secpresent && sectionDtl[0].totalTime != null && !sectionDtl[0].totalTime == "") {
                    getTimerDt(); // Initialize timer elements before using them
                    changeWholeTime(sectionDtl[0].totalTime * 60);
                    secTimerPresent = true;
                } else {
                    if (sectionMst.totalTime != null && !sectionMst.totalTime == "") {
                        getTimerDt(); // Initialize timer elements before using them
                        changeWholeTime(sectionMst.totalTime * 60);
                    }
                }
            }
        }
    }

    function sectionChange(){
        sectionSelectValue = document.getElementById("sectionSelect").value;
        sectionQuestions=[];
        for(var q=0;q<storingQaForWeb.length;q++){
            if (storingQaForWeb[q].subject == sectionSelectValue){
                sectionQuestions.push(storingQaForWeb[q]);
            }
        }
        var indexVal = questions.map(i => i.subject).indexOf(sectionSelectValue);
        $("#sectionDisplayName").text(sectionSelectValue);

        goToQue(indexVal+1);
        displayQueNumbers();
        checkQuestionState();
        var sectionTime = sectionDtl[document.getElementById("sectionSelect").selectedIndex].totalTime;
        if(secTimerPresent) {
            if (sectionTime == undefined) {
                forceSubmitTest();
            } else {
                getTimerDt(); // Initialize timer elements before using them
                changeWholeTime(sectionTime * 60);
                startTimerForTest();
            }
        }
    }

    function checkQuestionState(){
        var qansobj = qaObj.userAnswers;
        var markList=[];

        var mergedSubjects = qansobj.map(subject => {
            var otherSubject = sectionQuestions.find(element => element.id === subject.id)
            return  { ...subject, ...otherSubject }
        })

        for(var p=0;p<mergedSubjects.length;p++){
            if (mergedSubjects[p].subject == sectionSelectValue){
                markList.push(mergedSubjects[p]);
            }
        }
        markList.map(mark=>{
            if (mark.userOption!='-1'){
                document.getElementById('que-'+(mark.queIndex+1)).classList.add('answered');
            }
           if (mark.reviewedQ && mark.userOption !='-1'){
                document.getElementById('que-'+(mark.queIndex+1)).classList.add('pinMark');
                document.getElementById('que-'+(mark.queIndex+1)).classList.add('reviewed','markedOrange');
                $('#que-' + (mark.queIndex+1) ).find('.num-text').append('<i class="material-icons" style="color:#FF4141 ">push_pin</i>');
            }else if (mark.reviewedQ) {
                document.getElementById('que-'+(mark.queIndex+1)).classList.add('pinMark');
                document.getElementById('que-'+(mark.queIndex+1)).classList.add('reviewed');
                $('#que-' + (mark.queIndex+1) ).find('.num-text').append('<i class="material-icons">push_pin</i>');
            }
        })

    }

    function intersect(a, b) {
        return a.filter(Set.prototype.has, new Set(b));
    }
    function addSubjectDropDown(){
        var subjectArrList = [];
        var optionHtml="";

        for (var r=0;r<questions.length;r++){
            for(var t=0;t<subjectsList.length;t++){
                if (questions[r].subject == subjectsList[t]){
                    subjectArrList.push(questions[r].subject)
                }
            }
        }
        finalList = intersect(subjectArrList, subjectsList);
        finalList = finalList.filter(function (value, index, array) {
            return array.indexOf(value) === index;
        });

        for(var v=0;v<finalList.length;v++){
            optionHtml+="<option value='"+finalList[v]+"' data-sub='"+finalList[v]+"'>"+finalList[v]+"</option>";
        }

        document.getElementById("sectionSelect").innerHTML = optionHtml;
        sectionSelectValue = document.getElementById("sectionSelect").value;
    }
    //Start Quiz
    function startQuiz(){
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
        addSubjectDropDown();
        for(var q=0;q<storingQaForWeb.length;q++){
            if (storingQaForWeb[q].subject == sectionSelectValue){
                sectionQuestions.push(storingQaForWeb[q]);
            }
        }

        if((quizMode !='practice') && (quizMode !='testSeries')) {
            $('.preview-quiz').removeClass('d-lg-block').addClass('d-none');
            $('.practice-quiz-wrapper').css('margin','0 auto');
            $('.title-header').removeClass('d-lg-block').addClass('d-none');

            // check if userShowAudio is available
            if(typeof userShowAudio === 'function') {
                userShowAudio('/funlearn/showImage?id=2&fileName=opponentfound.mp3&imgType=audio');
            }
            if(quizMode=='test'){
                $('.quiz-profile .media').hide();
                $('.quiz-profile').addClass('justify-content-center');
                $('.progress-bar-vertical').addClass('d-none');
                setTimeout(showCounter, 1000);
            } else {

                $('.gamer-profile').addClass('d-block').removeClass('d-none');  //hide Profiles
                $('.playerbg .media ').addClass('animate__animated animate__fadeInLeft');//Player 1 slide animation
                $('.bot-wrapper .img-bot').addClass('animate__animated animate__fadeInUp');//Bot Animation
                setTimeout(slideBots, 1500);
                slidingAudio('/funlearn/showImage?id=2&fileName=opponentscroll.mp3&imgType=audio');
                setTimeout(showUser, 4000);
                setTimeout(showCounter, 5500);
                battleAudios('/funlearn/showImage?id=2&fileName=battle.mp3&imgType=audio');
            }
        }
        else if((quizMode =='practice')||(quizMode =='testSeries')) {
            displayObjects();
            quizDetails();
            displayQueNumbers();
        }

        if(quizType=='test'){
            if(sectionMst!=null&&!sectionMst==""){
                examMst=sectionMst;
                if((secpresent&&sectionDtl[0].totalTime!=null&&!sectionDtl[0].totalTime=="")||(sectionMst.totalTime!=null&&!sectionMst.totalTime=="")) {
                    startTimerForTest();
                    if(secpresent&&sectionDtl[0].totalTime!=null&&!sectionDtl[0].totalTime==""){
                        //disable subject selection
                        document.getElementById("sectionSelect").disabled=true;
                    }
                }
            }else{
                if(mcqTotalTime && mcqTotalTime != "" && mcqTotalTime != "null" && mcqTotalTime>0){
                    setupMobTestTimer()
                    // Check if we're on mobile and target the appropriate timer
                    if (window.innerWidth < 992) {
                        // On mobile, use the mobile timer
                        $("#testTimerSectionMob").removeAttr('style').addClass('d-flex').attr('style','gap:12px')
                    } else {
                        // On desktop, use the desktop timer
                        $("#testTimerSection").removeAttr('style').addClass('d-flex').attr('style','gap:12px')
                    }
                    getTimerDt(); // Initialize timer elements before using them
                    changeWholeTime(mcqTotalTime * 60);
                    startTimerForTest();
                }else{
                    $("#testTimerSectionWrapMobile").hide()
                }
            }
        }
    }

    function arrangeSection( a, b ) {
        if ( a.subject.toLowerCase() < b.subject.toLowerCase()){
            return -1;
        }
        if ( a.subject.toLowerCase() > b.subject.toLowerCase()){
            return 1;
        }
        return 0;
    }

    const range = (min, max) => {
        const arr = Array(max - min + 1)
            .fill(0)
            .map((_, i) => i + min);
        return arr;
    }


    function displayQueNumbers(){
        var htmlStr='';
        if (sectionPresent){
            $("#sectionDisplayName").text(sectionSelectValue);
            var index = questions.findIndex(item => item.subject === sectionSelectValue);
            var qList;
            var td = [];
            var lastIndex;
            for(var t=0;t<questions.length;t++){
                if (questions[t].subject == qaObj.userAnswers[t].subject){
                    td.push(qaObj.userAnswers[t].subject);
                }
            }
            lastIndex = td.lastIndexOf(sectionSelectValue)+1;

            qList = range(index+1,lastIndex);

            for(var i=0;i<qList.length;i++){
                qIndex=i;
                htmlStr+='<div class="num-wrapper"  id="que-'+qList[i]+'" onclick="goToQue('+qList[i]+')">'+
                    '<div class="num-text-div"></div>'+
                    '<div id="attempt-status">Not Attempted</div>'+
                    '</div>'+
                    '</div>';
                document.getElementById('total-que-tab').innerHTML=htmlStr;
            }
            var allQDIV = document.querySelectorAll('.num-text-div');
            for(var a=0;a<allQDIV.length;a++){
                allQDIV[a].innerHTML =  '<div class="num-text">'+(a+1)+'</div>';
            }
        }else{
            for(var i=1;i<questions.length+1;i++){
                qIndex=i;
                htmlStr+='<div class="num-wrapper" id="que-'+i+'" onclick="goToQue('+i+')">'+
                    '<div class="num-text">'+i+'</div>'+
                    '<div id="attempt-status">Not Attempted</div>'+
                    '</div>'+
                    '</div>';
                document.getElementById('total-que-tab').innerHTML=htmlStr;
                if(que_count===(questions.length - 1)){
                    if((quizMode =='practice')||(quizMode =='testSeries')){
                        $('#subNewBtn-1').removeClass('d-block').addClass('d-none');
                        $('#subNewBtn-2').removeClass('d-lg-block');
                        $('.btn-next-common').text('Finish').addClass('next-btn').attr('onclick','finish()');
                    }
                }
            }
        }

        displayObjects();
    }

    function displayObjects(){
        for(var i=0;i<questions.length;i++) {
            qIndex=i;
            que_id=questions[i].id;

            if (sectionPresent){
                qSubject = questions[i].subject;
            }

            if(questions[i].ans4 =='Yes'){
                correctOption = 4;
            }
            else if(questions[i].ans3 =='Yes') {
                correctOption = 3;
            }
            else if(questions[i].ans2 =='Yes'){
                correctOption = 2;
            }
            else if(questions[i].ans1 =='Yes'){
                correctOption = 1;
            }
            else{
                correctOption = 5;
            }
            userAnswersObj();
            testTimerObj();
        }
    }

    function goToQue(index){
        que_count=index-1;
        $('.show-explanation,.show-video-explanation').hide();
        $('.mcqChatBtns').removeClass('d-flex').addClass('d-none');

        if((quizMode =='practice')||(quizMode =='testSeries')){
            if(qaObj.userAnswers.length!=que_count) { //set state only if not available
                setState(que_count);
            }
        }
        $('#subNewBtn-1').removeClass('d-none').addClass('d-block');
        $('#subNewBtn-2').addClass('d-lg-block');
        $('.btn-next-common').text('Next').removeClass('next-btn').attr('onclick','nextQue()');
        if(que_count===(questions.length - 1)){
            if((quizMode =='practice')||(quizMode =='testSeries')){
                $('#subNewBtn-1').removeClass('d-block').addClass('d-none');
                $('#subNewBtn-2').removeClass('d-lg-block');
                $('.btn-next-common').text('Finish').addClass('next-btn').attr('onclick','finish()');
            }
        }
        closeExplanation();
        closeVideoExplanationQuiz();
        quizDetails();
        closePreview();
        if(ibookgpt){
            ibookgpt.closeChatWidget()
        }
    }

    //Explanation Show
    function showExplanation(index) {
        var answerOption1=replaceSymbols(questions[index].op1);
        answerOption1=checkLanguageAndImage(answerOption1);

        var answerOption2=replaceSymbols(questions[index].op2);
        answerOption2=checkLanguageAndImage(answerOption2);

        var answerOption3=replaceSymbols(questions[index].op3);
        answerOption3=checkLanguageAndImage(answerOption3);

        var answerOption4=replaceSymbols(questions[index].op4);
        answerOption4=checkLanguageAndImage(answerOption4);
        var answerOption5=replaceSymbols(questions[index].op5);
        answerOption5=checkLanguageAndImage(answerOption5);

        if(questions[index].answerDescription != ''){
            var explanation=replaceSymbols(questions[index].answerDescription);
            explanation=checkLanguageAndImage(explanation);
        }
        if(questions[index].explainLink != ''){
            var videoLink=questions[index].explainLink;
        }
        if(questions[index].ans1=='Yes'){
            que_answer=answerOption1;

        }
        else if(questions[index].ans2=='Yes'){
            que_answer=answerOption2;

        }
        else if(questions[index].ans3=='Yes'){
            que_answer=answerOption3;

        }
        else if(questions[index].ans4=='Yes'){
            que_answer=answerOption4;
        }
        else if(questions[index].ans5=='Yes'){
            que_answer=answerOption5;
        }


        if (quizMode == 'practice' || quizMode=="testSeries" || quizMode=="learn") {
            if (questions[index].answerDescription != '' && questions[index].answerDescription != 'null' && questions[index].answerDescription != null) {
                $('.show-explanation').show();
                explanation=replaceImageUrlForApps(explanation);
                $('#explanation').html(explanation);
                var heading = "<h4>Explanation</h4>";
                $('.show-explanation').html(heading+explanation);
                que_answer=replaceImageUrlForApps(que_answer);
                $('#correct-answer').html(que_answer);

            } else {
                $('.show-explanation').hide();
            }
            $('.mcqChatBtns').addClass('d-flex').removeClass('d-none');
            if(quizMode =='practice'){
                $('.exp').addClass('d-flex').removeClass('d-none');
                $('.squ').addClass('d-flex').removeClass('d-none');
            }

            if (questions[index].explainLink != '' && questions[index].explainLink != 'null' && questions[index].explainLink != null) {
                $('.show-video-explanation').show();
                if(videoLink.includes("/")){
                    var yt_url=videoLink;
                }else{
                    var yt_url = "https://www.youtube.com/embed/" + videoLink;
                }
                $('#videoExplanation').html('<iframe class="video_iframe" src="'+yt_url+'" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>');
            } else {
                $('.show-video-explanation').hide();
            }

        }

    }

    //Save User Answers and Bot Answers in Object
    var checkObj=false;
    var checkObj1=false;
    var botTime='';
    var timerArr=[];

    function userAnswersObj(){
        if((quizMode =='practice')||(quizMode =='testSeries') || (quizMode =='test')){
            botTime=0;
        }
        else{
            botTime=(timeValue/100)-chatBotAnswerTime;
        }

        botTotalTime=Number(botTotalTime)+botTime;
        var tempAnswerObj=qaObj.userAnswers;


        for(var i=0;i<tempAnswerObj.length;i++){
            if(tempAnswerObj[i].queIndex==qIndex){
                checkObj=true;
            }
            if(qIndex==tempAnswerObj.length){
                checkObj1=true;

            }
        }

        if(!checkObj) {//remove duplicates and update if obj if not available
            qaObj.userAnswers.push(
                {
                    id: que_id,
                    queIndex:qIndex,
                    correctOption: correctOption,
                    userOption: -1,
                    botTime: botTime,
                    userTime: 0,
                    botAnswer: botAnswer,
                    subject:qSubject,
                    reviewedQ:reviewedQ,
                    favouriteQ:favouriteQ
                }
            );
        }
    }

    var checkObjs=false;
    var  checkObjs1=false;
    function testTimerObj(){
        var timerArray=timerArr;
        for(var i=0;i<timerArray.length;i++){
            if(timerArray[i].queIndex==qIndex){
                checkObjs=true;
            }
            if(qIndex==timerArray.length){
                checkObjs1=true;

            }
        }
        if(!checkObjs) {
            timerArr.push({
                queIndex: qIndex,
                queTime: 0
            })
        }

    }

    //Shift to Next Que when timer Ends
    function nextQue(){
        if (secTimerPresent&&que_id==sectionQuestions[sectionQuestions.length-1].id && !(que_count===(questions.length - 1))){
            alert("Cannot move to next section till the time is complete.")
        }else {
            closeVideoExplanationQuiz();
            closeExplanation();
            clockPause();
            clockReset();
            if(ibookgpt){
                ibookgpt.closeChatWidget()
            }

            if (testGen) {
                language = language1.toLowerCase();
            }
            if (quizMode != '') {

                timerObj = timerArr[que_count];
                storeTimer = (timerObj.queTime) + (Number($('#test_timer').text()));
                sendTimer = {queTime: storeTimer};
                Object.entries(sendTimer).forEach(([key, value]) => {
                    timerObj[key] = value
                });

            }
            practicePrevious = false;
            $('.quizes').removeClass("animate__animated animate__bounceInRight animate__bounceInLeft");
            $('#countdown').text(questionTime);
            botAnswerFirst = false;
            userAnswerFirst = false;
            if (que_count < questions.length - 1) {
                que_count++;
                que_numb++;
                quizDetails();
            } else {

                if (quizMode == '') {
                    if ((source == "web" && (userId != ""||hasLibraryAccess)) || source != 'web') {
                        gameResult();
                    } else {
                        $('#review-modalDialog').modal('hide');
                        openRegisterForQuiz('gameResult');
                    }
                } else {
                    submitAnswers();
                }
            }

            if (sectionPresent) {

                if (que_id == sectionQuestions[sectionQuestions.length - 1].id && !(que_count === (questions.length - 1))) {

                    sectionQuestions = [];
                    var index = subjectsList.findIndex(item => item === sectionSelectValue);
                    sectionSelectValue = subjectsList[index + 1];
                    $("#sectionSelect").val(sectionSelectValue);
                    $("#sectionDisplayName").text(sectionSelectValue);
                    for (var q = 0; q < storingQaForWeb.length; q++) {
                        if (storingQaForWeb[q].subject == sectionSelectValue) {
                            sectionQuestions.push(storingQaForWeb[q]);
                        }
                    }
                    displayQueNumbers();
                    checkQuestionState();

                    var sectionTime = sectionDtl[document.getElementById("sectionSelect").selectedIndex].totalTime;
                    if (sectionTime == undefined) {
                        // do nothing
                    } else {
                        getTimerDt(); // Initialize timer elements before using them
                        changeWholeTime(sectionTime * 60);
                        startTimerForTest();
                    }
                }

            } else {
                if (que_count === (questions.length - 1)) {
                    if ((quizMode == 'practice') || (quizMode == 'testSeries')) {
                        $('#subNewBtn-1').removeClass('d-block').addClass('d-none');
                        $('#subNewBtn-2').removeClass('d-lg-block');
                        $('.btn-next-common').text('Finish').addClass('next-btn').attr('onclick', 'finish()');
                    }
                }
            }

            resetUIforNext();
            botAnsweredQues = false;
            $('.show-explanation,.show-video-explanation').hide();
            $('.mcqChatBtns').removeClass('d-flex').addClass('d-none');
            if ((quizMode == 'practice') || (quizMode == 'testSeries')) {

                if (qaObj.userAnswers.length != que_count) { //set state only if not available
                    setState(que_count);
                }
            }

            if (quizMode == 'testSeries' || quizMode == 'test') {
                $('.btn-review').removeClass().addClass('btn btn-review');
                $('.mrkrTab').removeClass('d-none');
            } else {
                $('.btn-review').removeClass().addClass('btn btn-review d-none');
            }
            if ($('#que-' + (que_count + 1)).hasClass('reviewed')) {
                $('.btn-review').addClass('marked');
            } else {
                $('.btn-review').removeClass('marked');
            }

            if (qaObj.userAnswers[que_count].userOption != -1) {
                $('.mcqChatBtns').addClass('d-flex').removeClass('d-none');
            }

            if (qaObj.userAnswers[que_count].favouriteQ) {
                document.getElementById(qaObj.userAnswers[que_count].id).classList.add('fa-solid', 'addedToFav');
            }
        }
    }

    $('.acw-header i').click(function(){
        if(learn){
            $('.btn-next-common').show();
            $('.app-footer .question-no').show();
            $('.app-footer .btn-prev-common').show();
        }
    });


    function backToHomeModal(){
        $('#closeTest').modal('show');
    }

    function finish(){
        if (!learn){
            $('#review-modalDialog').modal('show');
        }else{
            window.history.back();
        }

        var number_of_answered=[];
        var number_of_skipped=[];
        var number_of_reviewed=[];
        var qaAns = qaObj.userAnswers;
        for(var n=0;n<qaAns.length;n++){
            if (qaAns[n].userOption != '-1'){
                number_of_answered.push(qaAns[n]);
            }
            if (qaAns[n].userOption == '-1'){
                number_of_skipped.push(qaAns[n]);
            }
            if(qaAns[n].reviewedQ){
                number_of_reviewed.push(qaAns[n])
            }
        }
        var number_of_questions = questions.length;

        $('#noOfQuestions').html('Total Questions:'+number_of_questions);
        $('#noOfAnswered').text(number_of_answered.length);
        $('#noOfreviewed').text(number_of_reviewed.length);
        $('#noOfSkipped').text(number_of_skipped.length);
    }

    function submitFromModal(){
        testSubmitted=true;
        if((source=="web"&&(userId!=""||hasLibraryAccess))||source!='web') {
            if (!prepjoySite){
                $('.loading-icon').removeClass('hidden');
            }else{
                $('#loading').show();
            }
            $('.quizes').removeClass('d-flex').addClass('d-none');
            $('.app-header,.app-footer').hide();
            $('.preview-quiz').removeClass('d-flex');

            if (testGen) {
               submitTestGen(storingInitialData);
            } else {
                submitAnswers();
            }
        }else if(isFromExternal){
            submitAnswers();
        }else{
            if (!prepjoySite){
                $('#review-modalDialog').modal('hide');
                openRegisterForQuiz('submitFromModal');
            }else{
                $('#review-modalDialog').modal('hide');
                openRegisterForQuiz('submitFromModal');
            }


        }
    }

    function setState(que_count){
        var saveState=qaObj.userAnswers[que_count];
        var correctOption=saveState.correctOption;
        var userOption=saveState.userOption;
        var explanationState=questions[que_count].answerDescription;
        var explanationVideoState=questions[que_count].explainLink;

        if($('#que-'+(que_count+1)).hasClass('reviewed')){
            $('.btn-review').addClass('marked');
        }
        else{
            $('.btn-review').removeClass('marked');
        }
        if(userOption !=-1) {
            if(quizMode=='practice') {
                if (explanationState != '' && explanationState !=null && explanationState !='null') {
                    showExplanation(que_count);
                    $('.show-explanation').show();
                    $('.mcqChatBtns').addClass('d-flex').removeClass('d-none');
                } else {
                    $('.show-explanation').hide();
                    $('.mcqChatBtns').removeClass('d-flex').addClass('d-none');
                }

                if (explanationVideoState != '' && explanationVideoState !=null && explanationVideoState !='null') {
                    showExplanation(que_count);
                    $('.show-video-explanation').show();
                } else {
                    $('.show-video-explanation').hide();
                }
            }
            if (correctOption == userOption) {
                setTimeout(function () {
                    if(quizMode=='practice') {
                        setOption(correctOption, null);
                    }
                    else{
                        setOption(correctOption, userOption);
                    }
                }, 100);
            }
            else{
                setTimeout(function () {
                    setOption(correctOption,userOption);
                }, 100);
            }
        }

        if (qaObj.userAnswers[que_count].userOption!=-1){
            $('.mcqChatBtns').addClass('d-flex').removeClass('d-none');
        }
    }

    function prevQue(){
        if(secTimerPresent&&que_id==sectionQuestions[0].id) {
            alert("Cannot go to previous section");
        }else {
            closeVideoExplanationQuiz();
            closeExplanation();
            clockPause();
            clockReset();
            if(ibookgpt){
                ibookgpt.closeChatWidget()
            }

            if (testGen) {
                language = language1.toLowerCase();
            }
            if (quizMode != '') {
                timerObj = timerArr[que_count];

                if (que_count == (questions.length - 1)) {
                    $('.btn-next-common').attr('onclick', 'nextQue()').text('Next').removeClass('next-btn');
                }

                storeTimer = Number($('#test_timer').text());
                sendTimer = {queTime: storeTimer};
                Object.entries(sendTimer).forEach(([key, value]) => {
                    timerObj[key] = value
                });
            }

            practicePrevious = true;
            if (que_count == (questions.length - 1)) {
                $('#subNewBtn-1').removeClass('d-none').addClass('d-block');
                $('#subNewBtn-2').addClass('d-lg-block');
                $('.btn-next-common').attr('onclick', 'nextQue()').text('Next').removeClass('next-btn');
            }

            if (sectionPresent) {
                if (que_id == sectionQuestions[0].id) {
                    var index = subjectsList.findIndex(item => item === sectionSelectValue);
                    sectionSelectValue = subjectsList[index - 1];

                    $("#sectionSelect").val(sectionSelectValue);
                    $("#sectionDisplayName").text(sectionSelectValue);
                    sectionQuestions = [];
                    for (var q = 0; q < storingQaForWeb.length; q++) {
                        if (storingQaForWeb[q].subject == sectionSelectValue) {
                            sectionQuestions.push(storingQaForWeb[q]);
                        }
                    }
                    displayQueNumbers();
                    checkQuestionState();
                }
            }

            if (que_count != 0) {
                $('.quizes').removeClass("animate__animated animate__bounceInRight animate__bounceInLeft");
                que_count--;
                que_numb--;
                quizDetails();
                $('.show-explanation,.show-video-explanation').hide();
                $('.mcqChatBtns').removeClass('d-flex').addClass('d-none');
                setState(que_count);
            }
            if ($('#que-' + (que_count + 1)).hasClass('reviewed')) {
                $('.btn-review').addClass('marked');
            } else {
                $('.btn-review').removeClass('marked');
            }
            if (qaObj.userAnswers[que_count].userOption != '-1') {
                $('.mcqChatBtns').addClass('d-flex').removeClass('d-none');
            }

            if (qaObj.userAnswers[que_count].favouriteQ) {
                $('#fav-' + qaObj.userAnswers[que_count].id).addClass('fa-solid');
            }
        }
    }

    function setOption(correctOption,userOption){
        if(quizMode =='practice' || quizMode =='') {
            if(showCorrectAnswerState){


                $('.option').eq(correctOption - 1).addClass('option correct animate__bounceIn');
                showCorrectAnswerState=false;

            } else{

                $('.option').eq(correctOption - 1).addClass('correct');
            }
            disableOptions();
            if (userOption != null) {
                $('.option').eq(userOption - 1).addClass('incorrect');
            }
        }
        else if(quizMode=='testSeries'){
            $('.show-explanation,.show-video-explanation').hide();
            $('.mcqChatBtns').removeClass('d-flex').addClass('d-none');
            if (userOption != null) {
                $('.option').eq(userOption - 1).addClass('incorrect clicked');
            }
        }


    }

    //hide and animation when move to next ques
    function resetUIforNext(){
        $('#app,#countdown').show();
        $('.question-wrapper').removeClass("animate__animated animate__fadeInDown");
        $('.progress-bar-vertical.player1').removeClass('progress-correct progress-incorrect');
        $('.progress-bar-vertical.player2').removeClass('progress-correct progress-incorrect');
        $('#player1,#player2').css({'height':'0%','background': ''});
    }

    //Show timer and profile and start quiz.
    function quizDetails(){
        if(source!='web'){
            $('.audioChange').show();
        }
        else{
            $('.audioChange').hide();
        }
        $('.directions').addClass('d-none');
        if((quizMode !='practice') && (quizMode !='testSeries')) {
            $('.quiz-profile').removeClass('d-none').addClass('d-flex');
            $('.no-counter').addClass('d-none');
        }
        else if((quizMode =='practice')||(quizMode =='testSeries')){
            $('.app-footer .btn-prev-common').show();
            $('.app-footer .question-no').show();
            if($(window).width() <768) {
                $('.question-wrapper>.question-no').addClass('d-none');
            }

            if (que_count==0){
                // $('.btn-next-common').attr('onclick', 'nextQue()');
                $('.btn-next-common').text('Next').removeClass('next-btn').attr('onclick','nextQue()');
            }

        }
        if(quizMode=='testSeries'){
            $('.question-wrapper').addClass('test-series');
        }

        setTimeout(displayQuestions);
    }

    //Display Questions
    function displayQuestions(){
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
        $('.quizes').removeClass('d-none').addClass('d-flex');
        botDetails();
        if (quizMode==''){
            queAudio('/funlearn/showImage?id=2&fileName=questiondisplay.mp3&imgType=audio');
        }
        showQuestions(que_count);
        if(quizMode =='practice'){
            $('.mcqChatBtns').addClass('d-flex').removeClass('d-none');
            $('.exp').removeClass('d-flex').addClass('d-none');
            $('.squ').removeClass('d-flex').addClass('d-none');
        }
        if((quizMode !='practice') && (quizMode !='testSeries')) {
            setTimeout(answerAnimation,1000);
            setTimeout(removeAnimation,2000);
            setTimeout(startTimer, 2000);
            setTimeout(startTimers, 2000);
            setTimeout(addOptionSelectAttr,2000);
        }
        else if(quizMode =='testSeries'){
            $('.que-options').show();
            if(practicePrevious){
                $('.quizes').addClass("animate__animated animate__bounceInLeft");
            }
            else{
                $('.quizes').addClass("animate__animated animate__bounceInRight");
            }
            setTimeout(startTestTimer);
            setTimeout(addOptionSelectAttr,0);
        }
        else{
            $('.que-options').show();
            if(practicePrevious){
                $('.quizes').addClass("animate__animated animate__bounceInLeft");
            }
            else{
                $('.quizes').addClass("animate__animated animate__bounceInRight");
            }
            setTimeout(startTestTimer);
            setTimeout(addOptionSelectAttr,0);
        }

        if((userScore-botScore)>=2){

            botRun(1,chatBotAnswerTime);
        }
        else{

            botRun(botAnswer,chatBotAnswerTime);
        }
        addMathjax();
    }

    function removeAnimation(){
        $('.que-options .option:first-child').removeClass("animate__animated animate__fadeInDown");
        $('.que-options .option:nth-child(2)').removeClass("animate__animated animate__fadeInLeft");
        $('.que-options .option:nth-child(3)').removeClass("animate__animated animate__fadeInRight");
        $('.que-options .option:last-child').removeClass("animate__animated animate__fadeInUp");
    }

    //Gives bot's answer and time randomly
    function botDetails(){
        var botAnswers=[0,1];
        botAnswer=botAnswers[Math.floor(Math.random() * botAnswers.length) | 0];//Randomise Bot Answer 0=false,1=true
        var timerBot=(timeValue/100)-2;  //End time for bot answer
        chatBotAnswerTime = randomIntFromInterval(3,timerBot);//Randomise when bot to answer

    }

    var que_id;
    //Show Que and Answer one by one
    function showQuestions(index){
        if (testGen){
            $('.lang1').addClass('d-none');
            $('.lang2').addClass('d-none');
            if (questions[index].ps.includes("~~")){
                $('.lang1').removeClass('d-none')
            }else{
                $('.lang1').addClass('d-none')
                $('.lang2').addClass('d-none')
            }
        }else{
            $('.summaryTrans').removeClass('d-none');
        }
        qIndex=index;
        que_id=questions[index].id;
        $('.que-options').hide();
        $('.question-wrapper').hide();
        var que_no=$(".question-no");
        //Que-no
        que_no.text((Number(index)+1)+' of '+questions.length);
        totalNoOfQues = questions.length;
        var currQN = index+1;
        var percentageProgress = (currQN/totalNoOfQues)*100;
        $('.questionProgress').css('width',percentageProgress+"%");
        $('#quesList').html(currQN+" / "+totalNoOfQues);

        if(quizMode==''){
            document.querySelector('.audioSwitch').classList.remove('d-none');
            document.querySelector('.audioSwitch').classList.add('d-flex');
            if($(window).width()>768) {
                document.querySelector('.gameExit').classList.remove('d-none');
                document.querySelector('.gameExit').classList.add('d-flex');
            }
        }

        //que-text
        var que_text = document.querySelector(".que_text");
        var que_direction= document.querySelector("#direction");
        var btn_review = document.querySelector(".btn-review");
        var que_tag = replaceSymbols(questions[index].ps);
        var que_direction= document.querySelector("#direction");
        var favBtn = document.querySelector('.favQuestionStar');
        var directions=questions[index].directions;
        que_tag=checkLanguageAndImage(que_tag);

        var answerOption1=replaceSymbols(questions[index].op1);
        answerOption1=checkLanguageAndImage(answerOption1);

        var answerOption2=replaceSymbols(questions[index].op2);
        answerOption2=checkLanguageAndImage(answerOption2);

        var answerOption3=replaceSymbols(questions[index].op3);
        answerOption3=checkLanguageAndImage(answerOption3);

        var answerOption4=replaceSymbols(questions[index].op4);
        answerOption4=checkLanguageAndImage(answerOption4);
        var answerOption5=replaceSymbols(questions[index].op5);
        answerOption5=checkLanguageAndImage(answerOption5);

        if (source != 'web'){
            answerOption1 = replaceImageUrlForApps(answerOption1);
            answerOption2 = replaceImageUrlForApps(answerOption2);
            answerOption3 = replaceImageUrlForApps(answerOption3);
            answerOption4 = replaceImageUrlForApps(answerOption4);
            answerOption5 = replaceImageUrlForApps(answerOption5);
        }

        if(questions[index].ans1=='Yes'){
            que_answer=answerOption1;
            correctOption=1;
        }
        else if(questions[index].ans2=='Yes'){
            que_answer=answerOption2;
            correctOption=2;
        }
        else if(questions[index].ans3=='Yes'){
            que_answer=answerOption3;
            correctOption=3;
        }
        else if(questions[index].ans4=='Yes'){
            que_answer=answerOption4;
            correctOption=4;
        }
        else{
            que_answer=questions[index].op5;
            correctOption=5;
        }
        if(learn){
            if(questions[index].answerDescription!="" && questions[index].answerDescription!=null){
                $('.show-explanation').show();
                $('.mcqChatBtns').addClass('d-flex').removeClass('d-none');
                showExplanation(que_count);
            }
            if(questions[index].explainLink!="" && questions[index].explainLink!=null){
                $('.show-video-explanation').show();
                showExplanation(que_count);
            }
        }

        answerUI(answerOption1,answerOption2,answerOption3,answerOption4,answerOption5);

        que_text.innerHTML = que_tag;
        //checkFavouriteQuestions();


        if(directions !=null) {
            directions=replaceSymbols(directions);
            directions=checkLanguageAndImage(directions);
            directions=replaceImageUrlForApps(directions);
            que_direction.innerHTML = directions;
            if(que_direction.innerHTML.includes('Paragraph:')) {
                que_direction.innerHTML = que_direction.innerHTML.replace('Paragraph:', '<p style="font-weight: bold">Paragraph:</p>');
            }
            $('.directions').removeClass('d-none');
          //  showText();
        }
        $('.btn-review').addClass('btn-'+(index+1)).attr('onclick','markForReview('+index+')');
        option_list.innerHTML = option_tag;
        userAnswersObj();
        let favHeader = ""
        if(quizType=='test'){
            favHeader += "<div>"
            if (questions[index].marks && questions[index].marks!=="" && questions[index].marks!=null && questions[index].marks!=" ") {
                favHeader +="<span style='color: green;'><i class='fa-solid fa-check'></i> +" + questions[index].marks + "</span> ";
            }
            if (questions[index].negativeMarks && questions[index].negativeMarks!=="" && questions[index].negativeMarks!=null && questions[index].negativeMarks!=" "){
                favHeader += "<span> | </span>" +
                    "<span style='color: red;'><i class='fa-solid fa-xmark'></i> -"+questions[index].negativeMarks+"</span>";
            }
            favHeader +="</div>";
        }
        <sec:ifLoggedIn>
            if (qaObj.userAnswers[index].favouriteQ){
                favHeader += "<div style='margin-left: auto'><i class='fa-solid fa-star favStarBtn addedToFav' id='fav-"+questions[index].id+"' data-index="+index+"></i>"+
                    '<span class="tooltiptext">Mark as Unfavourite MCQ</span></div>';
            }else{
                favHeader += "<div style='margin-left: auto'><i class='fa-regular fa-star favStarBtn' id='fav-"+questions[index].id+"' data-index="+index+"></i>"+
                    '<span class="tooltiptext">Mark as favourite MCQ</span></div>';
            }

        </sec:ifLoggedIn>
        favBtn.innerHTML = favHeader;
        $('.question-wrapper').show();
        if((quizMode !='practice') && (quizMode !='testSeries')) {
            $('.question-wrapper').addClass("animate__animated animate__fadeInDown");
        }

        if (sectionPresent){
            if (que_id==sectionQuestions[sectionQuestions.length-1].id && que_count!=(questions.length - 1)){
                $('.btn-next-common').text('Next Section');
            }else if(que_count===(questions.length - 1)){
                $('#subNewBtn-1').removeClass('d-block').addClass('d-none');
                $('#subNewBtn-2').removeClass('d-lg-block');
                $('.btn-next-common').text('Finish').attr('onclick','finish()');
            }
            else{
                $('.btn-next-common').text('Next');
            }

            if ((sectionQuestions[0].id == que_id )&& que_count!=0){
                $('.btn-prev-common').text('Previous Section');
            }else{
                $('.btn-prev-common').text('Previous');
                $('.btn-prev-common').prop('disabled', false);
            }
        }

        if(que_id===questions[0].id){
            $('.btn-prev-common').attr('disabled','disabled');
        }else{
            $('.btn-prev-common').removeAttr('disabled');
        }

        if (learn){
            var allOptions = document.querySelectorAll('.option');
            for(var a=0;a<allOptions.length;a++){
                var learnAnsOption = $(allOptions[a]).attr("data-ans");
                allOptions[a].classList.add('disabled');
                allOptions[a].removeAttribute('onclick');
                if (learnAnsOption==correctOption){
                    allOptions[a].classList.add('correct');

                }
            }
        }
        <sec:ifLoggedIn>
            var favStarIcon = document.querySelector('.favStarBtn');
            favStarIcon.addEventListener('click',function (){
                if (favStarIcon.classList.contains('addedToFav')){
                    addRemoveFavourites(favStarIcon.id,favStarIcon.dataset.index,'remove','quiz');
                }else{
                    addRemoveFavourites(favStarIcon.id,favStarIcon.dataset.index,'add','quiz');
                }
            })

        </sec:ifLoggedIn>
    }

    function answerUI(answerOption1,answerOption2,answerOption3,answerOption4,answerOption5){

        option_tag = '<div class="option" data-ans="1"><span>'+ answerOption1.trim() +'</span></div>'
            + '<div class="option" data-ans="2"><span>'+ answerOption2.trim() +'</span></div>';
        if(answerOption3 !=''){
            option_tag += '<div class="option" data-ans="3"><span>'+ answerOption3.trim() +'</span></div>';
        }
        if(answerOption4 !=''){
            option_tag += '<div class="option" data-ans="4"><span>'+ answerOption4.trim() +'</span></div>';
        }
        if(answerOption5 !=''){
            option_tag+= '<div class="option" data-ans="5"><span>'+ answerOption5.trim() +'</span></div>';
        }

        return option_tag;
    }

    //Replace image url for apps
    function replaceImageUrlForApps(answerOptions){
        var myAnswerOption ='';

        if (answerOptions.indexOf('src') != -1){
            answerOptions = answerOptions.split('/funlearn/').join(baseUrl +'funlearn/');
            myAnswerOption = answerOptions;
        }
        else{
            myAnswerOption=answerOptions;
        }
        return myAnswerOption;
    }

    //check lang and image in response
    function checkLanguageAndImage(item){
        if(language=='hindi') {

            if (item.includes("~~")){
                item = item.substr(0,item.indexOf("~~"));
            }else{
                item = item;
                item =item.split('<span class="math-tex">').join('');
                item =item.split('</span>').join('');
            }

            // item = item + img;
        }else if(language=='kannada') {

            if (item.includes("~~")){
                item = item.substr(0,item.indexOf("~~"));
            }else{
                item = item;
                item =item.split('<span class="math-tex">').join('');
                item =item.split('</span>').join('');
            }
            // item = item + img;

        }else if(language=='english') {

            if (item.includes("~~")){
                item = item.substr(item.indexOf("~~")+2);
                item =item.split('<span class="math-tex">').join('');
                item =item.split('</span>').join('');
            }else{
                item = item.substr(0,item.indexOf("~~"));
            }
        }else{
            item = item;
            item =item.split('<span class="math-tex">').join('');
            item =item.split('</span>').join('');
        }
        if(item.indexOf('</table>') > -1){
            item=item.split("<table").join("<div class='table-responsive'><table class='table'");

            item=item.split('</table>').join('</table></div>');
        }

        return item;
    }

    //Check and replace symbols.
    function replaceSymbols(item){
        var replacedItem='';
        if(item !=null){
            replacedItem=item;
        }
        return replacedItem;
    }

    //Adding option select attributes
    function addOptionSelectAttr(){
        var option = option_list.querySelectorAll(".option");

        // set onclick attribute to all available options
        for(var i=0; i < option.length; i++){
            if (!learn){
                option[i].setAttribute("onclick", "optionSelected(this)");
            }
        }

    }

    // Start question timer
    function startTimer(){
        span = document.getElementById('countdown')
        countdown = new Countdown(span, questionTime);
        countdown.start();
    }

    function startTestTimer(){
        clockStart();
    }

    //User Answer Select
    var answerObj;
    var correcAns = '';
    var sendUserAnswer;
    var selectUserAnswerIndex='';
    var sendTimer;
    var timerObj;
    var storeTimer;

    function optionSelected(ans){
        $('.option').removeClass('correct incorrect clicked');
        var userAns = $(ans).attr("data-ans");

        answerObj=qaObj.userAnswers[que_count];
        timerObj=timerArr[que_count];
        correcAns = ''; //getting correct answer from array
        selectUserAnswerIndex='';
        if(questions[que_count].ans1=='Yes'){
            checkLangauge();
            correctOption=1;
           // que_answer=questions[que_count].op1;
        }
        else if(questions[que_count].ans2=='Yes'){
            checkLangauge();
            correctOption=2;
        }
        else if(questions[que_count].ans3=='Yes'){
            checkLangauge();
            correctOption=3;
        }
        else if(questions[que_count].ans4=='Yes'){
            checkLangauge();
            correctOption=4;
        }
        else{
            checkLangauge();
            correctOption=5;
        }

        if(quizMode!='') {
            storeTimer = (Number($('#test_timer').text()));
            sendTimer = {queTime: storeTimer};


            $.each(sendTimer,function(key,value){
                timerObj[key] = value;
            });//reassign values to particular id of answerobj

        }
        if(quizMode!='testSeries') {
            disableOptions();//disable once user selects answer
        }

        if((quizMode =='') || (quizMode =='test')){
            userPlay(newTime,userAns,correctOption,'',ans);//User play logic
        }
        if(quizMode =='test'){
            $('.progress-bar-vertical').addClass('d-none');
        }
        else if(quizMode =='testSeries' ){
            $('.progress-bar-vertical').addClass('d-none');
            userPlay(0,userAns,correctOption,storeTimer,ans);
        }
        else if((quizMode =='practice')){
            $('.progress-bar-vertical').addClass('d-none');
            $('.btn-next-common').attr('onclick','');
            $('.btn-prev-common').attr('onclick','');
            userPlay(0,userAns,correctOption,storeTimer,ans);
        }
        if((quizMode =='practice')) {
            showExplanation(que_count);
            checkAttempted(que_count + 1);
        }else if (quizMode =='testSeries'){
            checkAttempted(que_count + 1);
        }
        addMathjax();
    }

    function checkAttempted(que_count){
        $("#que-"+que_count).find('#attempt-status').text('Attempted');
        $("#que-"+que_count).css('border','1px solid #2980b9').addClass('answered');

        if (qaObj.userAnswers[que_count-1].userOption!='-1' && qaObj.userAnswers[que_count-1].reviewedQ){
            $('#que-' + (que_count)).addClass('markedOrange');
            $('#que-' + (que_count)+' i').css('color','#FF4141');
        }

    }

    function checkLangauge(){

        if(language=='hindi') {
            correcAns = correcAns.substr(0, correcAns.indexOf("~~"));
        }
        else if(language=='english') {
            correcAns = correcAns.substr(correcAns.indexOf("~~") + 2);
        }
        else{
            correcAns=questions[que_count].op1.trim();
        }
    }

    //Disable Options
    function disableOptions(){
        var allOptions = option_list.children.length; //getting all option items
        for(i=0; i < allOptions; i++){
            $(option_list.children[i]).attr("onclick", "");

        }
    }

    function randomIntFromInterval(min, max) { // min and max included
        return Math.floor(Math.random() * (max - min + 1) + min)
    }

    //Show Right Answers once bot and user answered
    var showCorrectAnswerState=false;
    //Show Right Answers once bot and user answered
    function showCorrectAnswer(){
        var allOptions = option_list.children.length; //getting all option items
        showCorrectAnswerState=true;
        for(i=0; i < allOptions; i++){

            var tempAnswer =  option_list.children[i].innerHTML; //getting user selected option
            tempAnswer = tempAnswer.replace('<span>','');
            tempAnswer = tempAnswer.replace('</span>','');

            tempAnswer=tempAnswer.trim();
            que_answer=que_answer.trim();
            que_answer=que_answer.trim();

            if(showCorrectAnswerState){
                setOption(correctOption, null);
            }

            if((quizMode =='practice')||(quizMode =='testSeries')){

                if(que_count===(questions.length - 1)){
                    $('.btn-next-common').attr('onclick','finish()');
                }
                else {
                    $('.btn-next-common').attr('onclick', 'nextQue()');
                }
                $('.btn-prev-common').attr('onclick','prevQue()');
            }
        }
        for(i=0; i < allOptions; i++){
            option_list.children[i].classList.add("disabled"); //once user select an option then disabled all options
        }
    }

    function closeExplanation(){
        $('.explanation-wrapper').addClass('animate__animated animate__bounceOutDown');
        setTimeout(resetExplanation,1000);
    }
    function closeVideoExplanationQuiz(){
        $('.video-explanation-wrapper').addClass('animate__animated animate__bounceOutDown');
        setTimeout(resetVideoExplanation,1000);
        var leg=$('.video_iframe').attr("src");
        $('.video_iframe').attr("src",leg);
    }

    function resetExplanation(){
        $('.explanation-wrapper').hide().removeClass('animate__animated animate__bounceOutDown');
    }

    function resetVideoExplanation(){
        $('.video-explanation-wrapper').hide().removeClass('animate__animated animate__bounceOutDown');
    }

    //Clear Timer and timer progress immediately
    function clearImmediate(){
        $('#app,#countdown').hide();
        $('#countdown').text('00');
        clearInterval(timeObj.timer); //clear counter
        clearTimerBar();
        document.getElementById("base-timer-path-remaining").setAttribute("stroke-dasharray", '283');
    }

    //mark for review
    function markForReview(index){
        index=Number(index);
        if (qaObj.userAnswers[index].userOption!='-1'){
            $('.btn-'+(index+1)).toggleClass('marked');
            $('#que-' + (index + 1)).toggleClass('pinMark markedOrange');
            if($('.btn-'+(index+1)).hasClass('marked')) {
                $('#que-' + (index + 1)).addClass('reviewed').find('.num-text').append('<i class="material-icons" style="color: #FF4141">push_pin</i>');
                qaObj.userAnswers[index].reviewedQ=true;
            }
            else{
                $('#que-' + (index + 1)).removeClass('reviewed').find('.num-text').html((index + 1));
                qaObj.userAnswers[index].reviewedQ=false;
            }
        }else{
            $('.btn-'+(index+1)).toggleClass('marked');
            $('#que-' + (index + 1)).toggleClass('pinMark');
            if($('.btn-'+(index+1)).hasClass('marked')) {
                $('#que-' + (index + 1)).addClass('reviewed').find('.num-text').append('<i class="material-icons">push_pin</i>');
                qaObj.userAnswers[index].reviewedQ=true;
            }
            else{
                $('#que-' + (index + 1)).removeClass('reviewed').find('.num-text').html((index + 1));
                qaObj.userAnswers[index].reviewedQ=false;
            }
        }
    }

    //Submit Answer To server
    function submitAnswers(){

        $('#review-modalDialog').modal('hide');
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        if(quizMode!=''){
            $('.loader-submit').show();
            if (!prepjoySite){
                $('.loading-icon').removeClass('hidden');
            }else{
                $('#loading').show();
            }
        }
        $('.preview-quiz').removeClass('d-lg-block').addClass('d-none');
        $('.practice-quiz-wrapper').css('margin','0 auto');
        $('.title-header').removeClass('d-lg-block').addClass('d-none');
        document.querySelector('.audioSwitch').classList.add('d-none');
        document.querySelector('.audioSwitch').classList.remove('d-flex');
        if($(window).width()>768) {
            document.querySelector('.gameExit').classList.add('d-none');
            document.querySelector('.gameExit').classList.remove('d-flex');
        }

        $('.displayQuestionNumber').addClass('d-none').removeClass('d-flex');

        submitQuiz();
    }

    //submit test generator quiz
    function submitTestGen(data){
        questions = data.results;
        $('#review-modalDialog').modal('hide');
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        if(quizMode!=''){
            $('.loader-submit').show();
            if (!prepjoySite){
                $('.loading-icon').removeClass('hidden');
            }else{
                $('#loading').show();
            }
        }
        $('.preview-quiz').removeClass('d-lg-block').addClass('d-none');
        $('.practice-quiz-wrapper').css('margin','0 auto');
        $('.title-header').removeClass('d-lg-block').addClass('d-none');
        submitQuiz();
    }

    function submitQuiz(){
        var score=0;
        var correctArr=[];
        var answerByUser=qaObj;
        var qaAnswers=answerByUser.userAnswers;
        console.log("qaAnswers",qaAnswers);
        var hasScore=false;
        if(ibookgpt){
            ibookgpt.closeChatWidget()
        }
        for(var i=0;i<questions.length;i++) {
            if (quizMode != '') {
                 if(qaAnswers[i].userOption==-1){
                    //unanswered. Right now do not do anything
                 }
                else if (qaAnswers[i].userOption == qaAnswers[i].correctOption) {
                    //correct answer
                    correctArr.push(qaAnswers[i].userOption);
                    if (typeof questions[i].marks !== 'undefined') {
                        hasScore=true;
                        score =Number(score)+Number(questions[i].marks);
                    }
                }
                else{
                    //incorrect answer
                    if (typeof questions[i].marks !== 'undefined') {
                        hasScore = true;
                        score = Number(score) - Number(questions[i].negativeMarks);
                    }
                }
            }
        }

        if(quizMode !=''){
            userScore=correctArr.length;
            if(!hasScore) score = correctArr.length;
        }

        $('#noofque').text(questions.length);
        if(fromShop=='disabled'){
            $('.button-results').addClass('d-none');
        }
       console.log('score',score);
       function getQuizDetails(params) {
            return {
                matchStatus: userWin,
                userTime: userTotalTime,
                botTime: botTotalTime,
                userPoints: userScore,
                botPoints: botScore,
                userName: userId,
                botName: botName,
                language: language,
                siteId: siteId,
                source: source,
                quizType: quizType,
                quizId: myQuizId,
                noOfQuestions: noOfQuestions,
                ...params
            };
        }

        let quizDetails;
        if (quizMode === '') {
            if (dailyTest) {
                quizDetails = getQuizDetails(realDailyTestDtlId ? { dailyTestDtlId: testId, realDailyTestDtlId: realDailyTestDtlId } : { dailyTestId: testId });
            } else if (weekly === 'yes' || monthly === 'yes') {
                quizDetails = getQuizDetails({ chapterId: chapterId });
            } else {
                quizDetails = getQuizDetails({ resId: resourcesId, chapterId: chapterId });
            }
        } else {
            if (dailyTest) {
                quizDetails = getQuizDetails(realDailyTestDtlId ? { dailyTestDtlId: testId, realDailyTestDtlId: realDailyTestDtlId } : { dailyTestDtlId: testId });
            } else if (weekly === 'yes' || monthly === 'yes') {
                quizDetails = getQuizDetails({ chapterId: chapterId });
            } else {
                quizDetails = getQuizDetails({ resId: resourcesId, chapterId: chapterId });
               <% if ("retest".equals(params.mode)) {%>
                    quizDetails.questionOptions = questionOptions;
                    quizDetails.parentQuizRecId = parentQuizRecId;
                <%}%>
            }
        }
        <%if(session.getAttribute("fromInstitutePageinstituteId")!=null){%>
        quizDetails.instituteId = "<%= session["fromInstitutePageinstituteId"] %>";
        <%}else if  (session["userInstituteId"]!=null){%>
        quizDetails.instituteId = "<%= session["userInstituteId"] %>";
        <%}%>

        if (testGen){
            qaObj.testGenId = testGenId
        }
        if(isFromExternal){
            qaObj.isFromExternal = true
        }else{
            qaObj.isFromExternal = false
        }

        if(fromLiveMockTest && fromLiveMockTest=='true'){
            qaObj.fromLiveMockTest = true;
        }
        qaObj.score = score;
        qaObj = Object.assign(qaObj,quizDetails);
        qaObj=toString(qaObj);

        if(source!='web'){
            submitCallback(qaObj);
            if(previous=='no'){
                $('.btn-prev').prop('disabled', true);
            }
            if(next=='no'){
                $('.btn-next').prop('disabled', true);
            }
        }
        else if ((userId!=""|| hasLibraryAccess && !isFromExternal) || (userId=="" && isFromExternal)) {
            var saveData = $.ajax({
                type: 'POST',
                url: "/prepjoy/quizSubmit",
                contentType: "application/json",
                dataType: "json",
                data: qaObj,
                success: function (response) {

                    if (!exitQuiz){
                        if (response.status == 'success') {
                            if(response.testGenId!=null) {
                                 window.location.href = "/onlineTest/listTests";
                                return;
                            }
                            resultHandler(response)
                        }
                    }else{
                        if (testGen && !TestGenBook){
                            window.location.href = "/test-generator";
                        }else{
                            if (fromPubDesk=='true' || analytics){
                                window.close();
                            }else{
                                window.close();
                            }
                        }
                    }
                }
            });
        }else if(userId =="" && !isFromExternal){
            window.close();
        }
    }

    //convert Json Obj to Strings
    function toString(o) {
        Object.keys(o).forEach(k => {
            if (typeof o[k] === 'object') {
                return toString(o[k]);
            }

            o[k] = '' + o[k];
        });

        return JSON.stringify(o);
    }

    function resultHandler(response) {
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
        $('#review-modalDialog').modal('hide');
        $('.loader-submit').hide();
        $('.quizes,.quiz-profile').removeClass('d-flex').addClass('d-none');
        $('.practice-container').addClass('d-none');
        $('.practice-result').show();
        $('.mcqChatBtns').removeClass('d-flex').addClass('d-none');
        quizStatisticsList = response ? response.quizStatisticsList : [];
        if(quizMode ==''){
            $('#winnerModal').modal('hide');
        }
        if(response){
            quizRecIdVal=response.quizRecId;
            bookLevel = response.level;
            bookGrade = response.grade;
            bookSyllabus = response.syllabus;
            bookSubject = response.subject;
            // Store showTestResults and testResultDate from response
            showTestResults = response.showTestResults !== undefined ? response.showTestResults : true;
            testResultDate = response.testResultDate || null;
            ranks = response.ranks;
            quizResName = response.quizName;
        }
        populateQuizResultUI();
        getQuizRelatedBooks()
    }
    //show Preview
    function showPreview(){

        $('.preview-quiz').removeClass('d-none').addClass('d-flex');
        $('.preview-quiz__header h4 i').hide();
        $('.practice-quiz-wrapper').addClass('d-none');

    }

    function closePreview(){
        $('.preview-quiz').removeClass('d-flex').addClass('d-none')
        $('.practice-quiz-wrapper').removeClass('d-none');
    }

    //lang select

    function langSelect(){
        var lang = document.getElementById("languageSelect");
        var langWeb = document.getElementById("languageSelectWeb");
        if(lang.value == "English"){
            language='english';
        }else if (lang.value == 'Hindi'){
            language='hindi';
        }else if(lang.value == 'Kannada'){
            language='kannada';
        }

        if(langWeb.value == "English"){
            language='english';
        }else if (langWeb.value == 'Hindi'){
            language='hindi';
        }else if(langWeb.value == 'Kannada'){
            language='kannada';
        }else{
            language='english';
        }

    }

    //timeSelect

    function timeSelect(){
        var time = document.getElementById("timeSelect");
        var timeWeb = document.getElementById("timeSelectWeb");
        changeTestTime(time.value);
        changeTestTime(timeWeb.value);
    }

    function onStart(){
        if (language1 !="" && language2 !="" && language1!=null && language2 !=null){
            if (language==language1.toLowerCase()){
                $('.lang1').removeClass('d-none');
            }else if (language==language2.toLowerCase()){
                $('.lang2').removeClass('d-none');
            }
        }
        audioHandler();
        var  checking = $("input[type='radio']:checked");
        if(checking.hasClass('first')) {
            $('.main-page').hide();
            if (!prepjoySite){
                $('.loading-icon').removeClass('hidden');
            }else{
                $('#loading').show();
            }
            if((quizMode=='') || (quizMode=='practice') ||(quizMode=='testSeries')){
                $('.practice-container').removeClass('d-none');
            }

            if ( defaultQueNo !=undefined &&  defaultQueNo !='undefined'&& defaultQueNo !='' && defaultQueNo !=null){
                questions=questions.slice(0,defaultQueNo);
            }
            startQuiz();
        } else if(checking.hasClass('second')) {

            defaultQueNo = $('#input-b').val();
            fromQueNo = $('#left-b').val();
            toQueNo = $('#right-b').val();
            if(( defaultQueNo !=undefined &&  defaultQueNo !='undefined'&& defaultQueNo !='' && defaultQueNo !=null && questions.length >= defaultQueNo ) || (fromQueNo != undefined && fromQueNo !='' && toQueNo != undefined && toQueNo != '' && questions.length >= toQueNo) ) {
                $('.main-page').hide();
                if (!prepjoySite){
                    $('.loading-icon').removeClass('hidden');
                }else{
                    $('#loading').show();
                }
                if((quizMode=='') || (quizMode=='practice') ||(quizMode=='testSeries')){
                    $('.practice-container').removeClass('d-none');
                }

                if ( defaultQueNo !=undefined &&  defaultQueNo !='undefined'&& defaultQueNo !='' && defaultQueNo !=null){
                    questions=questions.slice(0,defaultQueNo);
                }
                else if(( defaultQueNo == undefined || defaultQueNo =='undefined' || defaultQueNo =='' || defaultQueNo ==null) && fromQueNo != undefined && toQueNo != undefined )
                {
                    questions=questions.slice(fromQueNo-1,toQueNo);
                }
                startQuiz();
            } else{
                if(!isAppend) {
                    if (questions.length < toQueNo ||( questions.length < defaultQueNo)){
                        $('.button-container-selector').before('<div class="selectErr text-center">Please enter valid number of questions</div>');
                        isAppend = true;
                    }else {
                        $('.button-container-selector').before('<div class="selectErr text-center">Please enter number of questions</div>');
                        isAppend = true;
                    }
                }
            }
        }

    }

    //Formulas
    function addMathjax(){
        MathJax = { mml: { forceReparse: true } }
        $('head').append('<script src="https://polyfill.io/v3/polyfill.min.js?features=es6">')
        $('head').append('<script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js">')
    }

    //showing share b no we shoukl
    function showShareButton(){
        $('#sharebtn').removeClass('invisible');
    }

    //callback for shareicon showing
    function showShareIcon(){
        if(source=='android'){
            JSInterface.sharingModal();
        }
        else if(source=='ios'){
            webkit.messageHandlers.sharingModal.postMessage('share button');
        }
    }

    //callback for showing rating modal
    function showRating(){
        if(source=='android'){
            JSInterface.ratingModal();
        }
        else if(source=='ios'){
            webkit.messageHandlers.ratingModal.postMessage('rating modal');
        }
    }

    //Report question
    var questionId;
    var reportObj;
    var questionReportObj;

    function showReportModal(questionNo){
        questionId = questionNo;
        $('#prep-report-question').modal('show');
    }

    function reportSubmit(){
        var issuesSelected="";
        if(document.getElementById("spellingMistake").checked){
            issuesSelected="Spelling mistake";
        }
        if(document.getElementById("directionNotGiven").checked){
            issuesSelected+=(issuesSelected==""?"":",")+"Directions not given";
        }
        if(document.getElementById("imageNotVisible").checked){
            issuesSelected+=(issuesSelected==""?"":",")+"Image not visible";
        }
        if(document.getElementById("incompleQuestion").checked){
            issuesSelected+=(issuesSelected==""?"":",")+"Incomplete questions";
        }
        if(document.getElementById("otherIssues").checked){
            issuesSelected+=(issuesSelected==""?"":",")+"Other issues";
        }

        var moreInformation = document.getElementById("moreInformation").value;

        if (issuesSelected != "" || moreInformation!=""){
            $(".issueErr").hide();
            <g:remoteFunction controller="log" action="addQuizIssue" params="'id='+questionId+'&issuesList='+issuesSelected+'&issue='+moreInformation"  onSuccess="issueReported()"/>
        }else{
            $(".issueErr").show();
        }


        questionReportObj={
            selectedIssue:issuesSelected,
            questionIssueId:questionId,
            issuetext:moreInformation
        }
        questionReportObj=JSON.stringify(questionReportObj);
        if (source!='web'){
            reportQuiz(questionReportObj);
        }
    }

    function issueReported(){
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
        $('#prep-report-question').modal('hide');
        $("#report-success").modal('show');
    }

    //callback for reporting quiz questions
    function clearModal(){
        $('#moreInformation').val('');
        $("#spellingMistake,#directionNotGiven,#imageNotVisible, #incompleQuestion,#otherIssues").prop('checked', false);

    }

    function reportQuiz(reports){
        $('#prep-report-question').modal('hide');
        if(source=='android'){
            JSInterface.reportQuizIssue(reports);
        }
        else if(source=='ios'){
            webkit.messageHandlers.reportQuizIssue.postMessage(reports);
        }
    }

    function showAction(data){
        $('.action-wrapper').show();
        if(data=='rematch'){
            $('#btn-play').attr('onclick','playQuizAgain()');
            $('#btn-practice').attr('onclick','practiceQuiz()');
            $('#btn-test').attr('onclick','testQuiz()');
            $('#btn-learn').attr('onclick','learnQuiz()')
        }
        else if(data=='previousMatch'){
            $('#btn-play').attr('onclick','previousQuiz()');
            $('#btn-practice').attr('onclick','practicePreviousQuiz()');
            $('#btn-test').attr('onclick','testPreviousQuiz()');
        }
        else if(data=='nextMatch'){
            $('#btn-play').attr('onclick','nextQuiz()');
            $('#btn-practice').attr('onclick','practiceNextQuiz()');
            $('#btn-test').attr('onclick','testNextQuiz()');
        }
    }

    function practiceQuiz(){
        if (source == 'android') {
            JSInterface.practiceAgainQuiz();
        } else if (source == 'ios') {
            webkit.messageHandlers.practiceAgainQuiz.postMessage('');
        }else{
            quizMode = 'practice';
            learn=false;

            changeUrl();
        }
    }

    function testQuiz(){
        if (source == 'android') {
            JSInterface.testAgainQuiz();
        } else if (source == 'ios') {
            webkit.messageHandlers.testAgainQuiz.postMessage('');
        }else{
            quizMode = 'testSeries';
            learn=false;
            changeUrl();
        }
    }

    function learnQuiz(){
        if (source == 'android') {
            JSInterface.learnQuizAgain();
        } else if (source == 'ios') {
            webkit.messageHandlers.learnQuizAgain.postMessage('');
        }else{
            quizMode = 'practice';
            checkObj = false;
            learn=true;
            changeUrl();
        }
    }

    function playQuizAgain(){
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        if (quizMode==''){
            window.location.reload();
        }else{
            quizMode = '';
            learn=false;
            changeUrl();
        }

    }

    function practicePreviousQuiz(){
        if (source == 'android') {
            JSInterface.practicePreviousQuiz();
        } else if (source == 'ios') {
            webkit.messageHandlers.practicePreviousQuiz.postMessage('');
        }
    }

    function testPreviousQuiz(){
        if (source == 'android') {
            JSInterface.testPreviousQuiz();
        } else if (source == 'ios') {
            webkit.messageHandlers.testPreviousQuiz.postMessage('');
        }
    }

    function practiceNextQuiz(){
        if (source == 'android') {
            JSInterface.practiceNextQuiz();
        } else if (source == 'ios') {
            webkit.messageHandlers.practiceNextQuiz.postMessage('');
        }
    }

    function testNextQuiz(){
        if (source == 'android') {
            JSInterface.testNextQuiz();
        } else if (source == 'ios') {
            webkit.messageHandlers.testNextQuiz.postMessage('');
        }
    }

    function previousQuiz(){
        if(source!='web'){
            if(source =='android'){
                JSInterface.prevQuiz();
            }
            else{
                webkit.messageHandlers.prevQuiz.postMessage('');
            }
        }
    }

    function nextQuiz(){
        if(source!='web'){
            if(source =='android'){
                JSInterface.nextQuiz();
            }
            else{
                webkit.messageHandlers.nextQuiz.postMessage('');
            }
        }
    }

    function playAgain(){
        if(source=='android'){
            JSInterface.restartGame(nextChallenger,nextchallengerPlace);
        }
        else if(source=='ios'){
            webkit.messageHandlers.restartGame.postMessage('');
        }else if(source=='web'){
            window.location.reload();
        }
    }

    function closeButtons(){
        $('.action-wrapper').hide();
    }

    $(document).on('hide.bs.modal','#prep-report-question', function () {
        $('.form-check-input').prop('checked',false);
        $('#moreInformation').val('');

    });

    $("#toggle-theme").on('change', function() {
        if ($("#toggle-theme").is(':checked'))
            toggle();
        else {
            toggle();
        }
    });

    function toggle() {
        var a = document.getElementById("pagestyle");
        a.x = '/assets/prepJoy/prepJoy-lite' == a.x ? '/assets/prepJoy/prepJoy' : '/assets/prepJoy/prepJoy-lite';
        a.href = a.x + '.css';
    }

    function showText() {
        // Configure/customize these variables.
        var showChar = 250;  // How many characters are shown by default
        var ellipsestext = "";
        var moretext = "Show less";
        var lesstext = "Show more";


        $('.more').each(function() {
            var content = $(this).html();

            if(content.length > showChar) {
                if (content.includes('table')){
                    $(".morecontent").html(content);
                }else if (content.includes('<img>')){
                    $(".morecontent").html(content);
                }else{
                    var c = content.substr(0, showChar);
                    var h = content.substr(showChar);
                    var html = c + '<span class="moreellipses">' + ellipsestext+ '</span><span class="morecontent"></span><a href="" class="morelink less ml-3 mr-3">' + moretext + '</a>';
                    $(this).html(html);
                    $(".morecontent").html(h);
                }
            }

        });

        $(".morelink").click(function(){
            if($(this).hasClass("less")) {
                $(this).removeClass("less");
                $(this).html(lesstext);
                $(".morecontent").hide();
            } else {
                $(this).addClass("less");
                $(this).html(moretext);
                $(".morecontent").show();
            }
            return false;
        });
    }

    //CALLING RETRY MODAL
    function retryQuiz() {
        $('.action-wrapper').show();
    }


    //CHANGING URL FOR WEB
    function changeUrl(){
        var quizId = myQuizId;
        var resId = resourcesId;

        if (fromPubDesk == "true"){
            if (!learn){
                var updatedUrl= "?quizId="+quizId+"&resId="+resId+"&quizType="+quizMode+"&source=web&siteName=${session['entryController']}&learn=false&pubDesk=true&dailyTest=false";
            }else{
                var updatedUrl= "?quizId="+quizId+"&resId="+resId+"&quizType="+quizMode+"&source=web&siteName=${session['entryController']}&learn=true&pubDesk=true&dailyTest=false";
            }
        }else if (!dailyTest){
            if (!learn){
                var updatedUrl= "?quizId="+quizId+"&resId="+resId+"&quizType="+quizMode+"&source=web&siteName=${session['entryController']}&learn=false&pubDesk=false&dailyTest=false";
            }else{
                var updatedUrl= "?quizId="+quizId+"&resId="+resId+"&quizType="+quizMode+"&source=web&siteName=${session['entryController']}&learn=true&pubDesk=false&dailyTest=false";
            }
        }else if (dailyTest){
            if (!learn){
                var updatedUrl= "?dateInput="+dateInput+"&dailyTestId="+dailytestID+"&quizType="+quizMode+"&learn=false&pubDesk=false&dailyTest=true";
            }else{
                var updatedUrl= "?dateInput="+dateInput+"&dailyTestId="+dailytestID+"&quizType="+quizMode+"&learn=true&pubDesk=false&dailyTest=true";
            }
        }

        window.history.replaceState(null,null,updatedUrl);
        window.location.reload();
    }

    function onCancel(){
        window.history.back();
    }


     $('.show-select input').change(function(){
         var exactQues = $('#input-b').val();
         var fromQues = $('#left-b').val();
         var toQues = $('#right-b').val();


         $('.show-select input').keyup(function(){
             var limit = $('.first-header h3').text();
             var exactQues = $('#input-b').val();
             var fromQues = $('#left-b').val();
             var toQues = $('#right-b').val();

             if(parseInt(exactQues) !='' || parseInt(fromQues) != '' || parseInt(toQues) != '')
             {
                 $('.selectErr').empty();
                 isAppend = false;
             }

             if(parseInt(exactQues) > parseInt(limit) )
             {
                 $('#input-b').val('');
             }
             else if(parseInt(fromQues) >parseInt(limit))
             {
                 $('#left-b').val('');
             }
             else if(parseInt(toQues) > parseInt(limit))
             {
                 $('#right-b').val('');
             }
         })


         $('#input-b').keyup(function(){
             $('#left-b').val('');
             $('#right-b').val('');
         })


         $('.two-box-container input ').keyup(function(){
             $('#input-b').val('');


         })
         $('.two-box-container input ').change(function(){
             var fromQues = $('#left-b').val();
             var toQues = $('#right-b').val();
             if(fromQues !=0 && toQues !=0 )
             {
                 var fromQues = $('#left-b').val();
                 var toQues = $('#right-b').val();
                 if(parseInt(fromQues) > parseInt(toQues))
                 {

                     $('#left-b').val('');
                     $('#right-b').val('');
                 }
             }
         })



         if(((fromQues != '' && fromQues != 'undefined' && fromQues != undefined) && (toQues != '' && toQues != 'undefined' && toQues != undefined))||(exactQues != '' && exactQues != 'undefined' && exactQues != undefined) )
         {
             $('.selectErr').empty();
             isAppend = false;
         }

     })

    $('input:radio').change(function() {
       var  checking = $("input[type='radio']:checked");
       if(checking.hasClass('second')) {
           $('.show-select').removeClass('d-none');

       }
        if(checking.hasClass('first')) {
               $('.show-select').addClass('d-none');
               $('.selectErr').empty();
               $('.show-select input').val('');

        }
   })

    function nextPage(){

        var  checking = $("input[type='radio']:checked")
        if(checking.hasClass('first')) {
            $('#webMcq').removeClass('d-none');
            $('.selectErr').empty();
            $('#questionSelectionsTab').addClass('d-none');
        } else if(checking.hasClass('second')) {
            defaultQueNo = $('#input-b').val();
            fromQueNo = $('#left-b').val();
            toQueNo = $('#right-b').val();
            if(( defaultQueNo !=undefined &&  defaultQueNo !='undefined'&& defaultQueNo !='' && defaultQueNo !=null) ||(fromQueNo != undefined && fromQueNo !='' && toQueNo != undefined && toQueNo != '') ) {
                $('#webMcq').removeClass('d-none');
                $('#questionSelectionsTab').addClass('d-none');
                initializeQuizResults(quesList);
            } else{
                if(!isAppend) {
                    $('.button-container-selector').before('<div class="selectErr text-center">Please enter number of questions</div>');
                    isAppend = true;
                }
            }
        }
    }
    function showVideo(){
        $('.video-explanation-wrapper').show().addClass('animate__animated animate__bounceInUp');
    }

    function exitTest(){
        $('#closeTest').modal('hide');
        exitQuiz=true;
        submitQuiz();
    }

    function goBackToHome(){
        $('#mockTestAlreadyTakenModal').modal('hide');
        window.history.back();
    }

    $(document).ready(function() {
        <% if("arihant".equals(session["entryController"]) && session["userdetails"]==null && !"".equals(params.navMode) && params.navMode!=null){%>
        <% def newCookie = new javax.servlet.http.Cookie( "siteName", "${session["entryController"]}");
            newCookie.path = "/"
            response.addCookie newCookie;
            %>
        localStorage.setItem('quizlink','true');
        $('#signup').modal('show');
        operation="signup";
        $('#signup button.close, #loginOpen button.close, #forgotPasswordmodal button.close').hide();
        $('#goBackButton').attr('style','display:none !important');
        $('#signupMessage, #loginMessage').text("Please sign-up/login to attempt mock tests.").css('color','black');
        <%}%>

        // Check for mock test already taken condition
        <% if(askLogin) { %>
        $('#signup').modal('show');
        operation="signup";
        $('#signup button.close, #loginOpen button.close, #forgotPasswordmodal button.close').hide();
        $('#goBackButton').attr('style','display:none !important');
        $('#signupMessage, #loginMessage').text("Please sign-up/login to attempt mock tests.").css('color','black');
        <% } else if(alreadyAttemptedMock) { %>
        $('#mockTestAlreadyTakenModal').modal('show');
        <% } %>
    });

    function setupMobTestTimer(){
        if(quizType=='test') {
            // Initialize timer visibility based on screen size
            if (window.innerWidth < 992) {
                $("#testTimerSectionWrapMobile").show();
                $("#testTimerSectionWrap").html('');  // Clear desktop timer content
            } else {
                $("#testTimerSectionWrapMobile").hide();
                // Restore desktop timer content if it was cleared
                if ($("#testTimerSectionWrap").html().trim() === '') {
                    $("#testTimerSectionWrap").html(`
                    <div class="align-items-center svg-timer" id="testTimerSection" style="display: none">
                        <div class="normal-time">
                            <svg id="time-progress" width="35" height="35" viewBox="0 0 200 200" style="transform: rotate(-90deg)">
                                <circle cx="100" cy="100" r="90" stroke="#e0e0e0" stroke-width="20" fill="none"></circle>
                                <circle cx="100" cy="100" r="90" stroke="#76c7c0" stroke-width="20" fill="none" stroke-dasharray="565.48" stroke-dashoffset="565.48"></circle>
                            </svg>
                            <button class="play d-none" id="pause" data-setter=""></button>
                        </div>
                        <div class="sectiontime-wrapper">
                            <p class="timeLeft tot-time-text">Total Time Left</p>
                            <span class="c display-remain-time">00.00</span>
                        </div>
                    </div>
                `);
                }
            }

            // Handle window resize to show/hide appropriate timer
            $(window).resize(function() {
                if (window.innerWidth < 992) {
                    $("#testTimerSectionWrapMobile").show();
                    $("#testTimerSectionWrap").html('');  // Clear desktop timer content
                } else {
                    $("#testTimerSectionWrapMobile").hide();
                    // Restore desktop timer content if it was cleared
                    if ($("#testTimerSectionWrap").html().trim() === '') {
                        $("#testTimerSectionWrap").html(
                            '<div class="align-items-center svg-timer" id="testTimerSection" style="display: none">' +
                            '<div class="normal-time">' +
                            '<svg id="time-progress" width="35" height="35" viewBox="0 0 200 200" style="transform: rotate(-90deg)">' +
                            '<circle cx="100" cy="100" r="90" stroke="#e0e0e0" stroke-width="20" fill="none"></circle>' +
                            '<circle cx="100" cy="100" r="90" stroke="#76c7c0" stroke-width="20" fill="none" stroke-dasharray="565.48" stroke-dashoffset="565.48"></circle>' +
                            '</svg>' +
                            '<button class="play d-none" id="pause" data-setter=""></button>' +
                            '</div>' +
                            '<div class="sectiontime-wrapper">' +
                            '<p class="timeLeft tot-time-text">Total Time Left</p>' +
                            '<span class="c display-remain-time">00.00</span>' +
                            '</div>' +
                            '</div>'
                        );
                    }
                }
            });
        }
    }


    var gameTimer = document.getElementById('gameTimer');

    gameTimer.addEventListener('change',function (e){
        changeTestTime(e.target.value)
        // timeValue = e.target.value*100;
    })

    var gameSoundElement = document.getElementById('gameSound');
    gameSoundElement.addEventListener('change',function (){
        if (gameSoundElement.checked){
            gameSound = true;
        }else{
            gameSound = false;
        }
    })

    var audioSwitch = document.querySelector('.audioSwitch');
    audioSwitch.addEventListener('click',function (){
        var audioSwitchBtn = document.querySelector('.audioSwitch i');
        if (audioSwitchBtn.textContent=="volume_up"){
            audioSwitchBtn.textContent = "volume_off";
            gameSound=false;
            gameBackgroundMusic.pause();
        }else if(audioSwitchBtn.textContent=="volume_off"){
            audioSwitchBtn.textContent = "volume_up";
            gameSound=true;
            battleAudios('/funlearn/showImage?id=2&fileName=battle.mp3&imgType=audio');
        }
    })

    function audioHandler(){
        var audioSwitchBtn = document.querySelector('.audioSwitch i');
        if (gameSound){
            audioSwitchBtn.textContent = "volume_up";
        }else{
            audioSwitchBtn.textContent = "volume_off";
        }
    }

    var analytics=false;
    function showAllData(historyData){
        if(historyData.status=='Not Allowed'){
            window.location.href = "/onlineTest/listTests";
            return;
        }else {
            questions = historyData.results;
            historyData.userAnswers.map(item => {
                item.id = item.id.toString();
            })
            qaObj = JSON.stringify(historyData);
            storingInitialData = historyData;
            quizMode = historyData.quizType;
            language = historyData.language;
            analytics = true;
            quizRecIdVal = historyData.quizRecId;
            bookLevel = historyData.level;
            bookGrade = historyData.grade;
            bookSyllabus = historyData.syllabus;
            bookSubject = historyData.subject;
            quizStatisticsList = historyData.quizStatisticsList;
            ranks = historyData.ranks;
            quizId = historyData.quizId;
            myQuizId = historyData.quizId;
            resourcesId = historyData.resId;


            if (quizMode == 'testSeries' || quizMode == 'test') {
                $('.mrkrTab').removeClass('d-none');
            }
            $('body,html').css('background-color', '#060029 !important');
            $('.main-page').hide();
            $('.practice-container').removeClass('d-none');
            $('.app-header,.app-footer').hide();
            $('.quizes,.quiz-profile').removeClass('d-flex').addClass('d-none');
            $('.practice-container').addClass('d-none');
            $('.practice-result').show();
            $('.displayQuestionNumber').removeClass('d-none').addClass('d-flex');

            $('.preview-quiz').removeClass('d-lg-block').addClass('d-none');
            $('.practice-quiz-wrapper').css('margin', '0 auto');
            $('.title-header').removeClass('d-lg-block').addClass('d-none');
            $('#noofque').text(questions.length);

            qaObj = JSON.parse(qaObj);
            qaObj.userAnswers.map(item => {
                item.favouriteQ = "false";
            })
            qaObj = JSON.stringify(qaObj);
            populateQuizResultUI();
            getQuizRelatedBooks()
        }
    }

    function showGoogleExplanation(){
        var query = $('.que_text').text();
        var url ='http://www.google.com/search?q=' + query;
        window.open(url,'_blank');
    }

    var gameExitBtn = document.querySelector('.gameExitBtn');
    gameExitBtn.addEventListener('click',function (){
        backToHomeModal();
    })

    <%if(params.resId!=null){%>
    <sec:ifLoggedIn>
    updateUserView("${params.resId}","${params.quizType}_${params.fromTab}","${params.viewedFrom}");
    </sec:ifLoggedIn>
    <sec:ifNotLoggedIn>
    updateView("${params.resId}","${params.quizType}_${params.fromTab}","${params.viewedFrom}");
    </sec:ifNotLoggedIn>
   <%}%>
    function updateView(id,fromTab,viewedFrom){
        <g:remoteFunction controller="log" action="updateView" params="'id='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom" />
    }
    function updateUserView(id,fromTab,viewedFrom){
        <g:remoteFunction controller="log" action="updateUserView" params="'id='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom" />
    }


if ( document.getElementById('revise')!=null &&  document.getElementById('revise')!=undefined){
    document.getElementById('revise').addEventListener('click',function (){
        var markedCheckbox = document.getElementsByName('retest');
        var questionOptions=[];
        for (var checkbox of markedCheckbox) {
            if (checkbox.checked){
                questionOptions.push(checkbox.value);
            }
        }
        questionOptionsList = questionOptions.join(',');
        var inCrtCnt=0;
        var skpCnt=0;
        var crtCnt=0;
        if (questionOptions.length>0){
            if (!prepjoySite){
                $('.loading-icon').removeClass('hidden');
            }else{
                $('#loading').show();
            }
            var retestURL = "/resources/displayFlashCards?questionOptions="+questionOptionsList+"&parentQuizRecId="+quizRecIdVal+"&mode=retest";
            window.location.href = retestURL;
        }else{
            document.querySelector('.retestErrMsg').classList.remove('d-none');
            document.querySelector('.retestErrMsg').classList.add('d-flex');

            setTimeout(function (){
                document.querySelector('.retestErrMsg').classList.add('d-none');
                document.querySelector('.retestErrMsg').classList.remove('d-flex');
            },2000)
        }

    })
}
    function getQuizRelatedBooks(){
        var subscriptionBooks = false;
        var pageNo = 0;
        <g:remoteFunction controller="wonderpublish" action="getNewBooksList"  onSuccess='displayRelatedBooksUI(data);'
                  params="'categories=true&level='+bookLevel+'&syllabus='+bookSyllabus+'&grade='+bookGrade+'&subject='+bookSubject+'&pageNo='+pageNo+'&getSubscription='+subscriptionBooks" />
    }

    function displayRelatedBooksUI(data){
        var mcqEbooksList = JSON.parse(data.books);
        var colors=['#2EBAC6','#0D5FCE','#6FCF97','#F2C94C','#C20232','#FC7753','#E40039','#1abc9c','#FD7272','#55E6C1','#17c0eb'];
        var listPrice;
        var offerPrice;
    if(mcqEbooksList.length>0){
        mcqEbooksList = mcqEbooksList.slice(0,5);
        var booksHtml = "";
        for(var m=0;m<mcqEbooksList.length;m++){
            if (mcqEbooksList[m].listPrice == 0 || mcqEbooksList[m].listPrice == 0.0 || mcqEbooksList[m].listPrice == "null" || mcqEbooksList[m].listPrice == null || mcqEbooksList[m].listPrice == mcqEbooksList[m].offerPrice) {
                listPrice = "";
            } else {
                listPrice = "&#x20b9 " + mcqEbooksList[m].listPrice;
            }

            if (mcqEbooksList[m].offerPrice == 0){
                offerPrice = 'Free'
            }else{
                offerPrice = "&#x20b9 "+mcqEbooksList[m].offerPrice
            }
            let dtlLink = null;
            if(mcqEbooksList[m].detailPageURL){
                dtlLink  = mcqEbooksList[m].detailPageURL
            }

            let bookTitle = mcqEbooksList[m].title.replaceAll("'","\\'")
            booksHtml +="<div class='mcq__ebooks-cards__card'>";
            booksHtml +="   <div class='books__card-img' onclick=\"openBookDetailsPage('"+bookTitle+"','"+mcqEbooksList[m].id+"', '"+dtlLink+"')\">";

            if(mcqEbooksList[m].coverImage!=null && mcqEbooksList[m].coverImage!="null" && mcqEbooksList[m].coverImage.startsWith("https")){
                booksHtml +="    <img loading='lazy' src='"+mcqEbooksList[m].coverImage+"' alt='"+bookTitle+"'>";
            }else if(mcqEbooksList[m].coverImage!=null && mcqEbooksList[m].coverImage!="null" && mcqEbooksList[m].coverImage!=""){
                booksHtml +="    <img loading='lazy' src='/funlearn/showProfileImage?id="+mcqEbooksList[m].id+"&fileName="+mcqEbooksList[m].coverImage+"&type=books&imgType=webp' alt='"+bookTitle+"'>";
            }else{
                booksHtml +="     <div class='uncover'><p>"+mcqEbooksList[m].title+"</p></div>";
            }
            booksHtml +="</div>"+
                "<div class='books__card-details'>"+
                "   <h6 class='books__card-details__title' onclick=\"openBookDetailsPage('"+bookTitle+"','"+mcqEbooksList[m].id+"','"+dtlLink+"')\">"+mcqEbooksList[m].title+"</h6>";
            if(mcqEbooksList[m].publisher){
                booksHtml +="   <p class='books__card-details__publisher' onclick=\"openBookDetailsPage('"+bookTitle+"','"+mcqEbooksList[m].id+"','"+dtlLink+"')\">"+mcqEbooksList[m].publisher+"</p>";
            }

            booksHtml +="   <div onclick=\"openBookDetailsPage('"+bookTitle+"','"+mcqEbooksList[m].id+"','"+dtlLink+"')\">"+
                "      <span class='books-details__listPrice'>"+listPrice+"</span>"+
                "      <span class='books-details__sellingPrice'><strong>"+offerPrice+"</strong></span>"+
                "   </div>";
            booksHtml +=  "</div>"+
                "</div>";
        }
        if(mcqEbooksList[0].coverImage!=null && mcqEbooksList[0].coverImage!="null" && mcqEbooksList[0].coverImage.startsWith("https")){
            booksHtml +="<div class='mcq__ebooks-cards__card'><a class='mcq__ebooks-cards__card d-flex justify-content-center align-items-center showmoreCard' style='height: 300px;background: url("+mcqEbooksList[0].coverImage+") no-repeat center center /contain'>"+
                "<p>Show more</p>"+
                "</a></div>";
        }else if(mcqEbooksList[0].coverImage!=null && mcqEbooksList[0].coverImage!="null" && mcqEbooksList[0].coverImage!="" ){
            booksHtml +="<div class='mcq__ebooks-cards__card'><a class='mcq__ebooks-cards__card d-flex justify-content-center align-items-center showmoreCard' style='height: 300px;background: url(/funlearn/showProfileImage?id="+mcqEbooksList[0].id+"&fileName="+mcqEbooksList[0].coverImage+"&type=books&imgType=webp) no-repeat center center /contain'>"+
                "<p>Show more</p>"+
                "</a></div>";
        }else{
            booksHtml +="<div class='mcq__ebooks-cards__card'><a class='mcq__ebooks-cards__card d-flex justify-content-center align-items-center showmoreCard' style='height: 300px;background: #2EBAC6'>"+
                "<p>Show more</p>"+
                "</a></div>";
        }

        document.querySelector('.mcq__ebooks-cards').innerHTML = booksHtml;

        let cats = "";
        if(bookLevel!=null&&bookLevel!=""&&bookLevel!="null"){
            cats = 'level='+bookLevel.replaceAll(" ","-");
        }
        if(bookSyllabus!=null&&bookSyllabus!=""&&bookSyllabus!="null"){
            cats += '&syllabus='+bookSyllabus.replaceAll(" ","-");
        }
        if(bookGrade!=null&&bookGrade!=""&&bookGrade!="null"){
            cats += '&grade='+bookGrade.replaceAll(" ","-");
        }
        if(bookSubject!=null&&bookSubject!=""&&bookSubject!="null"){
            cats += '&subject='+bookSubject.replaceAll(" ","-");
        }
        cats += '&linkSource=mcq';
        let redBaseUrl = "";

        if(data.isAmazonList){
            cats = 'level='+bookLevel.replaceAll(" ","-");
        }
        <% if("books".equals(session["entryController"])||"90".equals(""+session["siteId"])){%>
            redBaseUrl = 'https://www.wonderslate.com/ebooks?';
        <%} else {%>
            if (!prepjoySite){
                <% if("true".equals(session["commonWhiteLabel"])){%>
                    redBaseUrl = '/sp/'+'${session['siteName']}'+'/store?'
                <%} else {%>
                    redBaseUrl = '/'+'${session['entryController']}'+'/store?'
                <%}%>
            }else{
                redBaseUrl = '/'+'${session['entryController']}'+'/eBooks?'
            }

        <%}%>

        const redirectionUrl = redBaseUrl + cats;
        document.querySelector('.showmoreCard').setAttribute('href',redirectionUrl);

        var fronts = document.querySelectorAll(".uncover");
        for(var i=0 ; i < fronts.length; i++) {
            fronts[i].style.background = colors[i%11];
        }
    }else{
        <% if("libwonder".equals(session["entryController"])){%>
            $('.ws_result-books').hide();
        <%}else {%>
            $('.quiz-related-books').hide();
        <%}%>
    }
}

async function getBooksFromAmazon(reqObj) {

    const searchQuery = reqObj.syllabus +" "+ reqObj.grade + " "+ reqObj.subject + " Books"
    const response = await fetch('/affiliation/getAmazonProducts?searchKey='+searchQuery)

    if(!response.ok){
        return false
    }

    const data = await response.json();

    const items = data.results.searchResult.items;

    const transformedItems = items.map((item, index) => {
        const title = item.itemInfo.title.displayValue;
        const coverImage = item.images.primary.medium.URL;
        let listPrice = item.offers.listings[0].price.amount;
        let offerPrice = item.offers.listings[0].price.savings?.amount || item.offers.listings[0].price.amount || 0;

        if(offerPrice<0){
            offerPrice = listPrice;
        }
        const detailPageURL = item.detailPageURL;

        return {
            id: item.ASIN,
            title: title,
            coverImage: coverImage,
            language: null,
            bookType:"print",
            subject: reqObj.subject,
            listPrice: listPrice,
            offerPrice: offerPrice,
            publisher: null,
            publisherId: null,
            grade: reqObj.grade ,
            syllabus: reqObj.syllabus,
            level: reqObj.level,
            medium: null,
            status: "published",
            allowSubscription: false,
            validityDays: "",
            hasQuiz: false,
            isbn: "",
            bookgptListPrice: listPrice,
            bookgptSellPrice: offerPrice,
            printOnly: false,
            detailPageURL: detailPageURL
        };
    });

    const resObj = {
        books:JSON.stringify(transformedItems),
        grade: reqObj.grade ,
        syllabus: reqObj.syllabus,
        level: reqObj.level,
        subject: reqObj.subject,
        publisher: null,
        publisherId: null,
        isAmazonList:true,
        bookTags:[]
    }
    displayRelatedBooksUI(resObj)
}

    function openBookDetailsPage(title,id,link){
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        var path = "";
        if(link == null || link == '' || link == 'null'){
            <%if("90".equals(""+session["siteId"])){%>
            path += "https://www.wonderslate.com/"+ title.replaceAll(' ','-') + "/ebook-details?siteName=books&bookId="+id+"&preview=true";
            <%}
         else if("true".equals(session["commonWhiteLabel"])){%>
            path += "/"+ title.replaceAll(' ','-') + "/ebook-details?siteName="+'${session['siteName']}'+"&bookId="+id+"&preview=true";
            <%} else {%>
            path += "/"+ title.replaceAll(' ','-') + "/ebook-details?siteName="+'${session['entryController']}'+"&bookId="+id+"&preview=true";
            <%}%>
        }else if(link!=null && link!="null"){
            path  = link
        }
        window.location.href = path;
    }

    function switchLanguage(){
        if (language == language1.toLowerCase()){
            language = language2.toLowerCase();
            displayQuestions();
            setState(que_count);
            $('.lang2').removeClass('d-none');
            $('.lang1').addClass('d-none');
        }else if (language == language2.toLowerCase()){
            language = language1.toLowerCase();
            displayQuestions();
            setState(que_count);
            $('.lang2').addClass('d-none');
            $('.lang1').removeClass('d-none');
        }
    }

    function switchResultLanguage(){
        if (languageToggleSwitch){
            languageToggleSwitch = false;
        }else if (!languageToggleSwitch){
            languageToggleSwitch = true;
        }

        if (language == language1.toLowerCase()){
            language = language2.toLowerCase();
            $('.lang2').removeClass('d-none');
            $('.lang1').addClass('d-none');
        }else if (language == language2.toLowerCase()){
            language = language1.toLowerCase();
            $('.lang2').addClass('d-none');
            $('.lang1').removeClass('d-none');
        }
        loadSolutions()
    }

    function closePage(){
        if (testGen && !TestGenBook){
            window.location.href = "/test-generator";
        }else{
            if (fromPubDesk=='true' || analytics){
                window.close();
            }else{
                window.close();
            }
        }
    }

    var favtype="";
    var favSource="";
    var favQuizid;
    function addRemoveFavourites(id,index,type,source){

        favQuizid = id.split('-')[1];
        var action = "";
        var favMcqLSAr=[];
        favtype = type;
        favSource = source;

        if (type=='add'){
            qaObj.userAnswers[Number(index)].favouriteQ = true;
            action='addUserMCQFavourite';
            var quizId = localStorage.getItem('favMcq');

            if (quizId!=null && quizId!=undefined){
                quizId = JSON.parse(quizId);
                quizId.push(Number(favQuizid));
                localStorage.setItem('favMcq',JSON.stringify(quizId));
            }else{
                qaObj.userAnswers[Number(index)].favouriteQ = false;
                favMcqLSAr.push(Number(favQuizid))
                localStorage.setItem('favMcq',JSON.stringify(favMcqLSAr));
            }
        }else{
            action = 'removeUserMCQFavourite';
            removeFavQuiz(Number(favQuizid));
        }
        favouritesUIUpdate();
        <g:remoteFunction controller="usermanagement" action="'+action+'" params="'questionId='+favQuizid" />
    }

    function favouritesUIUpdate(data){
        if (favSource=='quiz'){
            if (favtype=='add'){
                document.querySelector('.favStarBtn').classList.remove('fa-regular');
                document.querySelector('.favStarBtn').classList.add('fa-solid','addedToFav');
                document.querySelector('.tooltiptext').textContent = 'Mark as unfavourite';
            }else{
                document.querySelector('.favStarBtn').classList.remove('fa-solid','addedToFav');
                document.querySelector('.favStarBtn').classList.add('fa-regular');
                document.querySelector('.tooltiptext').textContent = 'Mark as favourite';
            }
        }else {
            if (favtype=='add'){
                document.getElementById('favRes-'+Number(favQuizid)).classList.remove('fa-regular');
                document.getElementById('favRes-'+Number(favQuizid)).classList.add('fa-solid','addedToFav');
                //document.querySelector('.tooltiptext').textContent = 'Mark as unfavourite';
            }else{
                document.getElementById('favRes-'+Number(favQuizid)).classList.remove('fa-solid','addedToFav');
                document.getElementById('favRes-'+Number(favQuizid)).classList.add('fa-regular');
                //document.querySelector('.tooltiptext').textContent = 'Mark as favourite';
            }
        }

    }

    function checkFavouriteQuestions() {
        var favQuizList = localStorage.getItem('favMcq');
        if (favQuizList!=null && favQuizList!=undefined){
            favQuizList = JSON.parse(favQuizList);
            updateFavMCQs(favQuizList)
        }
    }

    function updateFavMCQs(data) {
        var favList = data;
        var quizList = questions;
        var quizIds=[];
        var commonIds=[];

        quizList.forEach(item=>{
            quizIds.push(item.id)
        })
        for(let i of favList){
            if(quizIds.includes(i)){
                commonIds.push(i)
            }
        }
        for(var u=0;u<commonIds.length;u++){
            if (commonIds[u]==que_id){
                var item = qaObj.userAnswers.find(item=>item.id==que_id);
                item.favouriteQ=true;
            }
        }
    }

    function removeFavQuiz(id) {
        var quizId = localStorage.getItem('favMcq');
        if (quizId!=null && quizId!=undefined){
            quizId = JSON.parse(quizId);
            quizId = quizId.filter(item => item !== id);
            localStorage.setItem('favMcq',JSON.stringify(quizId));
        }
    }

    function continueTest() {
        $('#continue-test').modal('show');
    }
    function submitTest() {
        answeredUnanswered();
        $('#submit-test').modal('show');
    }
    function forceSubmitTest() {

        if(testGen) {
            submitFromModal();
        }
        else $('#force-submit-test').modal('show');
    }

    function nextSection() {
        document.getElementById("sectionSelect").selectedIndex += 1;
        sectionChange();
    }
    function askDoubt(type,index) {
        let quObj = 0;
        if(typeof qaObj == 'string'){
            qaObj = JSON.parse(qaObj)
        }
        if(qaObj && qaObj.userAnswers){
            quObj = qaObj.userAnswers[que_count].id
        }
        if((siteId=="71" && gpt == "true" && checkTokens()) || (tempSiteId!="71")){
            ibookgpt.mcqChat(quObj, type, index)
        }else{
            $("#rechargeModal").modal("show")
        }
    }

    document.addEventListener('keydown', event => {
        if (event.ctrlKey || event.metaKey && event.key === 'c') {
            event.preventDefault();
            event.stopPropagation();
            return false;
        }
    },false);
    document.addEventListener('keydown', event => {
        if (event.ctrlKey || event.metaKey && event.key === 's') {
            event.preventDefault();
            event.stopPropagation();
            return false;
        }
    },false);
    document.addEventListener('contextmenu', function(event) {
        event.preventDefault();
        return false;
    }, false);

    <%if("1".equals(""+session["siteId"]) || "3".equals(""+session["siteId"]) || "37".equals(""+session["siteId"]) || "71".equals(""+session["siteId"])){%>
    if ('matchMedia' in window) {
        // Chrome, Firefox, and IE 10 support mediaMatch listeners
        window.matchMedia('print').addListener(function(media) {
            console.log("pringgg",media);
            if (media.matches) {
                beforeBookPrint();
            } else {
                // Fires immediately, so wait for the first mouse movement
                $(document).one('mouseover', afterBookPrint);
            }
        });
    } else {
        // IE and Firefox fire before/after events
        $(window).on('beforeprint', beforeBookPrint);
        $(window).on('afterprint', afterBookPrint);
    }
    function beforeBookPrint() {
        $("html,body").hide();
    }
    function afterBookPrint() {
        $("html,body").show();
    }
    <%}%>
</script>

<%if("testSeries".equals(params.quizType)){%>
<asset:javascript src="timer.js"/>
<asset:javascript src="totalTimer.js"/>
<asset:javascript src="moment.min.js"/>
<%}%>
<script src="/assets/prepJoy/quizTimer.js"></script>
<script src="/assets/prepJoy/lottie.min.js"></script>
<script src="/assets/prepJoy/userPlay.js"></script>
<script src="/assets/prepJoy/botPlay.js"></script>
<script src="/assets/prepJoy/animation.js"></script>
<script src="/assets/prepJoy/quizResult.js"></script>
<script src="/assets/prepJoy/appCallback.js"></script>
<script src="/assets/prepjoyTimer.js"></script>


</body>
</html>
